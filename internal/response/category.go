package response

// CategoryTypeResp 分类类型响应
type CategoryTypeResp struct {
	Id          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Priority    int64  `json:"priority"`
}

// CategoryResp 分类响应
type CategoryResp struct {
	Id             string `json:"id"`
	Name           string `json:"name"`
	Description    string `json:"description"`
	Priority       int64  `json:"priority"`
	CategoryTypeId string `json:"categoryTypeId"`
}
