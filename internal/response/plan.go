package response

import "loop/pkg/types"

// UserQuestionnaireResp represents the response for a user's questionnaire
type UserQuestionnaireResp struct {
	Id                string `json:"id"`
	MotivationSource  string `json:"motivationSource"`
	DesiredAbility    string `json:"desiredAbility"`
	CurrentLevel      string `json:"currentLevel"`
	TargetLevel       string `json:"targetLevel"`
	DailyStudyMinutes int    `json:"dailyStudyMinutes"`
	CreatedAt         string `json:"createdAt"`
	UpdatedAt         string `json:"updatedAt"`
}

// LearningLevelResp represents a learning level
type LearningLevelResp struct {
	Id          uint   `json:"id"`
	Code        string `json:"code"`
	Name        string `json:"name"`
	Description string `json:"description"`
	ParentCode  string `json:"parentCode,omitempty"`
}

// PlanResourceResp represents a resource in a learning plan
type PlanResourceResp struct {
	ResourceId    string `json:"resourceId"`
	ResourceName  string `json:"resourceName"`
	ResourceCover string `json:"resourceCover,omitempty"`
	LsCount       int    `json:"lsCount"`
}

// PlanWeekResourceResp represents a resource in a weekly plan
type PlanWeekResourceResp struct {
	ResourceId    string `json:"resourceId"`
	ResourceName  string `json:"resourceName"`
	ResourceCover string `json:"resourceCover,omitempty"`
	LsCount       int    `json:"lsCount"`
}

// PlanDayResourceResp represents a resource in a day plan
type PlanDayResourceResp struct {
	ResourceId    string `json:"resourceId"`
	ResourceName  string `json:"resourceName"`
	ResourceCover string `json:"resourceCover"`
	ResourceUrl   string `json:"resourceUrl"`
}

// PlanDayResp represents a day in a learning plan week
type PlanDayResp struct {
	Id                 string                       `json:"id"`
	PlanDayNumber      int                          `json:"planDayNumber"`    // 学习天在整个计划中的顺序，从1开始
	WeekDayNumber      int                          `json:"weekDayNumber"`    // 学习天在周中的顺序，从1开始
	Status             int                          `json:"status"`           // 学习状态 0未开始 1进行中 2已完成
	CurrentSentences   int                          `json:"currentSentences"` // 当前已学习句数
	TargetSentences    int                          `json:"targetSentences"`  // 目标学习句数
	StudyTimestamp     int64                        `json:"studyTimestamp"`   // 学习日期（0点时间戳，毫秒）
	Type               int                          `json:"type"`             // 天的类型：1(学习日) 或 2(休息日)
	Resources          []PlanDayResourceResp        `json:"resources"`        // 该天的资源详细信息
	RecordedRanges     types.VideoTimeIntervalArray `json:"recordedRanges"`   // 录音句子时间范围数组
	AverageScore       int64                        `json:"averageScore"`
	TotalLearnDuration int64                        `json:"totalLearnDuration"`
}

// PlanWeekResp represents a week in a learning plan stage
type PlanWeekResp struct {
	WeekNumber int           `json:"weekNumber"`
	Days       []PlanDayResp `json:"days"` // 该周的学习天数据
}

// PlanStageResp represents a stage in a learning plan
type PlanStageResp struct {
	Id        string         `json:"id"`
	StageDesc string         `json:"stageDesc"` // 阶段描述，如 A0-1, A1-2 等
	Objective string         `json:"objective"`
	Weeks     []PlanWeekResp `json:"weeks,omitempty"` // 按周划分的学习计划
}

// LearningPlanResp represents a learning plan
type LearningPlanResp struct {
	Id                  string          `json:"id"`
	StartLevel          string          `json:"startLevel"`
	TargetLevel         string          `json:"targetLevel"`
	StartTimestamp      int64           `json:"startTimestamp"` // 开始日期（0点时间戳，毫秒）
	EndTimestamp        int64           `json:"endTimestamp"`   // 结束日期（0点时间戳，毫秒）
	Status              int             `json:"status"`
	TotalLearnDays      int             `json:"totalLearnDays"`
	StudyDaysOfWeek     *types.IntArray `json:"studyDaysOfWeek"`
	DailySentences      int             `json:"dailySentences"`
	CurrentDayTimestamp int64           `json:"currentDayTimestamp"` // 当前服务器时间的0点时间戳（毫秒）
	Stages              []PlanStageResp `json:"stages"`
	CreatedAt           string          `json:"createdAt"`
}

// RecordedRangesResp 录音句子时间范围数组响应结构
type RecordedRangesResp struct {
	Ranges types.VideoTimeIntervalArray `json:"ranges"` // 录音句子时间范围数组
}
