package response

// UserFeedbackResp 用户反馈响应
type UserFeedbackResp struct {
	Id          string `json:"id"`
	Content     string `json:"content"`
	LogOssUrl   string `json:"logOssUrl"`
	ContactInfo string `json:"contactInfo"`
	Status      int    `json:"status"`
	DeviceInfo  string `json:"deviceInfo"`
	AppVersion  string `json:"appVersion"`
}

// FeedbackListResp 反馈列表响应（管理员）
type FeedbackListResp struct {
	Id          string `json:"id"`
	Uid         string `json:"uid"`
	Content     string `json:"content"`
	LogOssUrl   string `json:"logOssUrl"`
	ContactInfo string `json:"contactInfo"`
	Status      int    `json:"status"`
	DeviceInfo  string `json:"deviceInfo"`
	AppVersion  string `json:"appVersion"`
}

// FeedbackDetailResp 反馈详情响应
type FeedbackDetailResp struct {
	Id          string `json:"id"`
	Uid         string `json:"uid"`
	Content     string `json:"content"`
	LogOssUrl   string `json:"logOssUrl"`
	ContactInfo string `json:"contactInfo"`
	Status      int    `json:"status"`
}
