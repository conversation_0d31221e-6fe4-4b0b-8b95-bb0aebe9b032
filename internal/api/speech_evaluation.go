package api

import (
	"loop/internal/data"
	"loop/internal/request"

	"github.com/gin-gonic/gin"
)

type SpeechEvaluationApi struct {
	Apis
	repo *data.SpeechEvaluationData
}

func NewSpeechEvaluationApi(repo *data.SpeechEvaluationData) *SpeechEvaluationApi {
	return &SpeechEvaluationApi{repo: repo}
}

// 创建评测
func (a *SpeechEvaluationApi) Create(c *gin.Context) {
	var req request.SpeechEvaluationReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.CreateFromReq(c, req)
	})
}

// 获取评测列表
func (a *SpeechEvaluationApi) List(c *gin.Context) {
	var req request.SpeechEvaluationListReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.ListByReq(c, req)
	})
}

// 更新评测
func (a *SpeechEvaluationApi) Update(c *gin.Context) {
	var req request.SpeechEvaluationUpdateReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.UpdateFromReq(c, req)
	})
}

// 批量创建评测
func (a *SpeechEvaluationApi) BatchCreate(c *gin.Context) {
	var req request.SpeechEvaluationBatchReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.BatchCreateFromReq(c, req)
	})
}

// 批量更新评测
func (a *SpeechEvaluationApi) BatchUpdate(c *gin.Context) {
	var req request.SpeechEvaluationBatchUpdateReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.BatchUpdateFromReq(c, req)
	})
}

// 批量更新AudioUrl
func (a *SpeechEvaluationApi) BatchUpdateAudioUrl(c *gin.Context) {
	var req request.SpeechEvaluationBatchUpdateAudioUrlReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.BatchUpdateAudioUrlFromReq(c, req)
	})
}
