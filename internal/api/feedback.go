package api

import (
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/request"

	"github.com/gin-gonic/gin"
)

func NewFeedbackApi(repo *data.FeedbackRepo, model *model.DbModel) *FeedbackApi {
	return &FeedbackApi{repo: repo, model: model}
}

type FeedbackApi struct {
	Apis
	model *model.DbModel
	repo  *data.FeedbackRepo
}

// CreateFeedback 创建反馈
func (a *FeedbackApi) CreateFeedback(c *gin.Context) {
	var req request.CreateFeedbackReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.CreateFeedback(c, req)
	})
}

// GetUserFeedbacks 获取用户反馈列表
func (a *FeedbackApi) GetUserFeedbacks(c *gin.Context) {
	var req request.EmptyReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetUserFeedbacks(c)
	})
}
