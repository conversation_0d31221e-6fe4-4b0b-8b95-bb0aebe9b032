package api

import (
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/request"

	"github.com/gin-gonic/gin"
)

func NewDataCenterApi(repo *data.DataCenterRepo, model *model.DbModel) *DataCenterApi {
	return &DataCenterApi{repo: repo}
}

type DataCenterApi struct {
	Apis
	repo *data.DataCenterRepo
}

func (a *DataCenterApi) DataEpisodeEachAdd(c *gin.Context) {
	var req request.DataEpisodeEachReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.DataEpisodeEachAdd(c, req)
	})
}
func (a *DataCenterApi) DataEpisodeModLsTime(c *gin.Context) {
	var req request.DataEpisodeModLsTimeReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.DataEpisodeUpdate(c, req)
	})
}
func (a *DataCenterApi) DataEpisodeHome(c *gin.Context) {
	var req request.DataEpisodeTypeReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.DataEpisodeHome(c, req)
	})
}
func (a *DataCenterApi) DataEpisodeList(c *gin.Context) {
	var req request.EmptyReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.DataEpisodeList(c, req)
	})
}
func (a *DataCenterApi) GetEpisodeLsData(c *gin.Context) {
	var req request.EpisodeLsDataReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetEpisodeLsData(c, req)
	})
}

func (a *DataCenterApi) DataEpisodeByChart(c *gin.Context) {
	var req request.DataEpisodeTypeReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.DataEpisodeByChart(c, req)
	})
}
