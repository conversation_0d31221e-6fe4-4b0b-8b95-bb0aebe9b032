package api

import (
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/request"

	"github.com/gin-gonic/gin"
)

func NewFeedbackAdminApi(repo *data.FeedbackRepo, model *model.DbModel) *FeedbackAdminApi {
	return &FeedbackAdminApi{repo: repo, model: model}
}

type FeedbackAdminApi struct {
	Apis
	model *model.DbModel
	repo  *data.FeedbackRepo
}

// GetFeedbackList 获取反馈列表
func (a *FeedbackAdminApi) GetFeedbackList(c *gin.Context) {
	var req request.FeedbackListReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetFeedbackList(c, req)
	})
}

// GetFeedbackById 获取反馈详情
func (a *FeedbackAdminApi) GetFeedbackById(c *gin.Context) {
	var req request.IdReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetFeedbackById(c, req.Id)
	})
}

// UpdateFeedbackStatus 更新反馈状态
func (a *FeedbackAdminApi) UpdateFeedbackStatus(c *gin.Context) {
	var req request.UpdateFeedbackStatusReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.UpdateFeedbackStatus(c, req)
	})
}
