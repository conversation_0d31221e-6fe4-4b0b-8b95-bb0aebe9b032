package api

import (
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/request"

	"github.com/gin-gonic/gin"
)

func NewCategoryAdminApi(repo *data.CategoryAdminRepo, model *model.DbModel) *CategoryAdminApi {
	return &CategoryAdminApi{repo: repo, model: model}
}

type CategoryAdminApi struct {
	Apis
	model *model.DbModel
	repo  *data.CategoryAdminRepo
}

func (a *CategoryAdminApi) Add(c *gin.Context) {
	var req request.ResCategoryReq
	a.processRequest(c, &req, func(r any) any {
		return data.SaveOneOrUpdate[model.Category](a.model, c, req, req.Id)
	})
}
func (a *CategoryAdminApi) GetById(c *gin.Context) {
	var req request.IdReq
	a.processRequest(c, &req, func(r any) any {
		return data.GetOne[model.Category](a.model, req.Id)
	})
}
func (a *CategoryAdminApi) Delete(c *gin.Context) {
	var req request.IdReq
	a.processRequestQuery(c, &req, func(r any) any {
		return data.DeleteById[model.Category](a.model, req.Id)
	})
}
func (a *CategoryAdminApi) DeleteMulti(c *gin.Context) {
	var req request.IdsReq
	a.processRequestQuery(c, &req, func(r any) any {
		return data.DeleteMultiId[model.Category](a.model, req.Id)
	})
}
func (a *CategoryAdminApi) GetCategoryList(c *gin.Context) {
	var req request.GetCategoryListReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetCategoryList(c, req)
	})
}
func (a *CategoryAdminApi) SetPriority(c *gin.Context) {
	var req request.PriorityReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.SetPriority(c, req)
	})
}
