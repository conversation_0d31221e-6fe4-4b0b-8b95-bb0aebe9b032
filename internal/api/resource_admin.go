package api

import (
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/request"

	"github.com/gin-gonic/gin"
)

func NewResourceAdminApi(repo *data.ResourceAdminRepo, model *model.DbModel) *ResourceAdminApi {
	return &ResourceAdminApi{repo: repo, model: model}
}

type ResourceAdminApi struct {
	Apis
	model *model.DbModel
	repo  *data.ResourceAdminRepo
}

func (a *ResourceAdminApi) Add(c *gin.Context) {
	var req request.ResourceAddReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.SaveOne(c, req)
	})
}
func (a *ResourceAdminApi) UpdateResource(c *gin.Context) {
	var req request.NativeLangReq
	a.processRequest(c, &req, func(r any) any {
		return data.UpdateOne[model.Resource](a.model, req)
	})
}
func (a *ResourceAdminApi) Delete(c *gin.Context) {
	var req request.IdReq
	a.processRequestQuery(c, &req, func(r any) any {
		return a.repo.DeleteResource(c, req)
	})
}
func (a *ResourceAdminApi) DeleteMulti(c *gin.Context) {
	var req request.IdsReq
	a.processRequestQuery(c, &req, func(r any) any {
		return a.repo.DeleteMultiResource(c, req)
	})
}
func (a *ResourceAdminApi) GetById(c *gin.Context) {
	var req request.IdReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetResourceDetail(c, req)
	})
}

func (a *ResourceAdminApi) SetFeaturedContent(c *gin.Context) {
	var req request.ResourcesFeaturedContentReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.SetFeaturedContent(c, req)
	})
}
func (a *ResourceAdminApi) GetResourceList(c *gin.Context) {
	var req request.ResourceListReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetResourceList(c, req)
	})
}

func (a *ResourceAdminApi) SetPriority(c *gin.Context) {
	var req request.PriorityReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.SetPriority(c, req)
	})
}
