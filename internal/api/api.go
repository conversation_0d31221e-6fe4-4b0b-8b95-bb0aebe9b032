package api

import (
	"loop/pkg/web"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/wire"
	"github.com/sirupsen/logrus"
)

var ProviderSet = wire.NewSet(
	NewUserApi, NewHomeApi, NewResourceApi, NewCategoryApi, NewCategoryTypeApi, NewV1Api,
	NewVideoApi, NewNoteApi, NewCollectApi, NewAIApi,
	NewDataCenterApi, NewSeriesApi,
	NewUserAdminApi, NewSeriesAdminApi, NewCategoryAdminApi, NewCategoryTypeAdminApi, NewResourceAdminApi,
	NewVipApi, NewBenefitApi, NewPlanApi,
	NewSpeechEvaluationApi, NewFeedbackApi, NewFeedbackAdminApi,
)

type Apis struct {
}

// processRequest 是一个高阶函数，它接收一个处理业务逻辑的函数
// 提取公共处理逻辑
// 修改 bindFunc 参数签名
func (a *Apis) processRequestCommon(
	c *gin.Context,
	req any,
	bindFunc func(any) error, // 修改点：去掉上下文参数
	processFunc func(any) any,
) {
	if err := bindFunc(req); err != nil { // 调用时只传 req
		logrus.Error("processRequest Error: ", err)
		AbortBadRequest(c, err)
		return
	}

	res := processFunc(req)
	if res == nil {
		AbortEntityNotFound(c)
		return
	}

	// 统一错误处理
	if result, ok := res.(*web.JsonResult); ok {
		switch {
		case result.Error != "":
			c.JSON(http.StatusInternalServerError, gin.H{
				"message": result.Error, // 增加 message 字段
				"code":    http.StatusInternalServerError,
			})
		case result.Code >= 400:
			c.JSON(result.Code, gin.H{
				"message": result.Msg, // 使用原有消息字段
				"code":    result.Code,
			})
		default:
			c.JSON(http.StatusOK, result)
		}
	} else if errResult, ok := res.(error); ok { // 新增兜底处理
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": errResult.Error(),
			"code":  http.StatusInternalServerError,
		})
	} else {
		c.JSON(http.StatusOK, res)
	}

}

// 重构后的三个方法
func (a *Apis) processRequest(c *gin.Context, req any, processFunc func(any) any) {
	a.processRequestCommon(c, req, c.ShouldBind, processFunc)
}

// 调用方式保持不变
func (a *Apis) processRequestJson(c *gin.Context, req any, processFunc func(any) any) {
	a.processRequestCommon(c, req, c.ShouldBindJSON, processFunc)
}

func (a *Apis) processRequestQuery(c *gin.Context, req any, processFunc func(any) any) {
	a.processRequestCommon(c, req, c.ShouldBindQuery, processFunc)
}
