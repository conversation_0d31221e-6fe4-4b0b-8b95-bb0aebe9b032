package api

import (
	"encoding/json"
	"loop/internal/data"
	"loop/internal/request"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay/apple"
	"github.com/sirupsen/logrus"
)

func NewVipApi(repo *data.VipRepo) *VipApi {
	return &VipApi{repo: repo}
}

type VipApi struct {
	Apis
	repo *data.VipRepo
}

func (a *VipApi) GetProductList(c *gin.Context) {
	var req request.EmptyReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetProductList(c)
	})
}
func (a *VipApi) ExchangeCode(c *gin.Context) {
	var req request.ExchangeCodeReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.ExchangeCode(c, req)
	})
}
func (a *VipApi) AddPromotionCode(c *gin.Context) {
	var req request.AddPromotionCodeReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.AddPromotionCode(c, req)
	})
}
func (a *VipApi) DeletePromotionCode(c *gin.Context) {
	var req request.IdReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.DeletePromotionCode(c, req)
	})
}
func (a *VipApi) DeleteMultiPromotionCode(c *gin.Context) {
	var req request.IdsReq
	a.processRequestQuery(c, &req, func(r any) any {
		return a.repo.DeleteMultiPromotionCode(c, req)
	})
}

func (a *VipApi) GetPromotionCodeList(c *gin.Context) {
	var req request.ListReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetPromotionCodeList(c, req)
	})
}

func (a *VipApi) GetUserVipInfo(c *gin.Context) {
	a.processRequest(c, nil, func(r any) any {
		return a.repo.GetUserVipInfo(c)
	})
}

func (a *VipApi) GetUserVipFlowList(c *gin.Context) {
	a.processRequest(c, nil, func(r any) any {
		return a.repo.GetUserVipFlowList(c)
	})
}

func (a *VipApi) GetOrderList(c *gin.Context) {
	a.processRequest(c, nil, func(r any) any {
		return a.repo.GetOrderList(c)
	})
}

func (a *VipApi) CreatePayOrder(c *gin.Context) {
	var reqJson request.CreatePayOrderReq
	a.processRequest(c, &reqJson, func(r any) any {
		return a.repo.CreatePayOrder(c, reqJson)
	})
}

type MyNotificationV2Payload struct {
	NotificationType string  `json:"notificationType"`
	Subtype          string  `json:"subtype"`
	NotificationUUID string  `json:"notificationUUID"`
	Version          string  `json:"version"`
	Data             *MyData `json:"data"`
}
type MyData struct {
	AppAppleID            int                   `json:"appAppleId"`
	BundleID              string                `json:"bundleId"`
	BundleVersion         string                `json:"bundleVersion"`
	Environment           string                `json:"environment"`
	SignedRenewalInfo     apple.RenewalInfo     `json:"signedRenewalInfo"`
	SignedTransactionInfo apple.TransactionInfo `json:"signedTransactionInfo"`
}

// 直接看signedTransactionInfo部分就够了，其他的没啥用
func (a *VipApi) GetPayFromApple(c *gin.Context) {
	logrus.Info("into GetPayFromApple")
	var reqJson struct {
		SignedPayload string `json:"signedPayload" binding:"required"`
	}

	if err := c.ShouldBindJSON(&reqJson); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid payload"})
		return
	}
	// decode signedPayload
	payload, err := apple.DecodeSignedPayload(reqJson.SignedPayload)
	if err != nil {
		logrus.Error(err)
		return
	}
	logrus.Printf("payload.NotificationType: %s", payload.NotificationType)
	logrus.Printf("payload.Subtype: %s", payload.Subtype)
	logrus.Printf("payload.NotificationUUID: %s", payload.NotificationUUID)
	logrus.Printf("payload.Data: %+v", payload.Data)
	bs1, _ := json.Marshal(payload)
	logrus.Info(string(bs1))

	// decode renewalInfo
	renewalInfo, err := payload.DecodeRenewalInfo()
	if err != nil {
		logrus.Error(err)
		return
	}
	bs, _ := json.Marshal(renewalInfo)
	logrus.Info("renewalInfo=", string(bs))

	// decode transactionInfo
	transactionInfo, err := payload.DecodeTransactionInfo()
	if err != nil {
		logrus.Error(err)
		return
	}
	bs2, _ := json.Marshal(transactionInfo)
	logrus.Info("transactionInfo=", string(bs2))

	c.JSON(http.StatusOK, a.repo.ReceiveApplePayResult(c, payload, renewalInfo, transactionInfo))
	// jsonData, err := json.Marshal(reqJson)
	// if err != nil {
	// 	logrus.Fatalf("JSON 转换失败: %v", err)
	// }

	// 这里可以将地址转发到本机地址来进行支付测试
	// ngrok http http://localhost:3000

	// url := "https://ff6a-183-247-6-137.ngrok-free.app/api/v1/vip/apple"
	// req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	// if err != nil {
	// 	logrus.Fatalf("创建请求失败: %v", err)
	// }

	// // 添加头信息
	// req.Header.Set("Content-Type", "application/json")
	// req.Header.Set("platform", "ios")
	// req.Header.Set("Authorization", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVaWQiOiIxODU4NDY5Mjk1OTQ0NjU4OTQ0IiwiVXNlcm5hbWUiOiJtaWFveW9uZ2p1biIsIlN0YXR1cyI6MCwiZXhwIjoxNzM0NzU3ODczLCJpYXQiOjE3MzIxNjU4NzN9.GkKxL211xwjlrUWXzWlAS-Bxjdw72jnS6ke-vVLVh9g")

	// // 发送请求
	// client := &http.Client{}
	// resp, err := client.Do(req)
	// if err != nil {
	// 	logrus.Fatalf("HTTP 请求失败: %v", err)
	// }
	// defer resp.Body.Close()
	// c.JSON(http.StatusOK, "OKKKKKKK")
}
