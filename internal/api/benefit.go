package api

import (
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/request"
	"strconv"

	"github.com/gin-gonic/gin"
)

func NewBenefitApi(repo *data.BenefitRepo, benefitModel *model.BenefitModel) *BenefitApi {
	return &BenefitApi{
		repo:         repo,
		benefitModel: benefitModel,
	}
}

type BenefitApi struct {
	Apis
	repo         *data.BenefitRepo
	benefitModel *model.BenefitModel
}

// CreateBenefit 创建权益
func (a *BenefitApi) CreateBenefit(c *gin.Context) {
	var req request.CreateBenefitReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.CreateBenefit(req)
	})
}

// UpdateBenefit 更新权益
func (a *BenefitApi) UpdateBenefit(c *gin.Context) {
	var req request.UpdateBenefitReq
	a.processRequest(c, &req, func(r any) any {
		return data.SaveOneOrUpdate[model.Benefit](a.benefitModel.DbModel, c, req, strconv.Itoa(int(req.Id)))
	})
}

// GetAllBenefits 获取所有权益
func (a *BenefitApi) GetAllBenefits(c *gin.Context) {
	var req request.BenefitPageReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetAllBenefits(req)
	})
}

// CreateBenefitGroup 创建权益组
func (a *BenefitApi) CreateBenefitGroup(c *gin.Context) {
	var req request.CreateBenefitGroupReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.CreateBenefitGroup(req)
	})
}

// UpdateBenefitGroup 更新权益组
func (a *BenefitApi) UpdateBenefitGroup(c *gin.Context) {
	var req request.UpdateBenefitGroupReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.UpdateBenefitGroup(req)
	})
}

// GetAllBenefitGroups 获取所有权益组
func (a *BenefitApi) GetAllBenefitGroups(c *gin.Context) {
	var req request.BenefitGroupPageReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetAllBenefitGroups(req)
	})
}

// UpdateBenefitGroupStatus 更新权益组状态
func (a *BenefitApi) UpdateBenefitGroupStatus(c *gin.Context) {
	var req request.UpdateBenefitGroupStatusReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.UpdateBenefitGroupStatus(req)
	})
}

// GetBenefitsByGroupCode 根据权益组编码获取权益列表
func (a *BenefitApi) GetBenefitsByGroupCode(c *gin.Context) {
	var req request.GetBenefitsByGroupCodeReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetBenefitsByGroupCode(req)
	})
}

// GetUserBenefitsList 获取用户权益列表
func (a *BenefitApi) GetUserBenefitsList(c *gin.Context) {
	var req request.UserBenefitListReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetUserBenefitsList(req)
	})
}

// GetUserBenefitDetail 获取用户权益详情
func (a *BenefitApi) GetUserBenefitDetail(c *gin.Context) {
	var req request.UserBenefitDetailReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetUserBenefitDetail(req)
	})
}
