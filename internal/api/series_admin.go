package api

import (
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/request"

	"github.com/gin-gonic/gin"
)

func NewSeriesAdminApi(repo *data.SeriesAdminRepo, model *model.DbModel) *SeriesAdminApi {
	return &SeriesAdminApi{repo: repo, model: model}
}

type SeriesAdminApi struct {
	Apis
	model *model.DbModel
	repo  *data.SeriesAdminRepo
}

func (a *SeriesAdminApi) GetById(c *gin.Context) {
	var req request.IdReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetSeriesDetail(c, req)
	})
}

func (a *SeriesAdminApi) Delete(c *gin.Context) {
	var req request.IdReq
	a.processRequestQuery(c, &req, func(r any) any {
		return a.repo.DeleteSeries(c, req)
	})
}
func (a *SeriesAdminApi) DeleteMulti(c *gin.Context) {
	var req request.IdsReq
	a.processRequestQuery(c, &req, func(r any) any {
		return a.repo.DeleteMultiSeries(c, req)
	})
}
func (a *SeriesAdminApi) SaveOneOrUpdate(c *gin.Context) {
	var req request.SeriesAddReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.SaveOne(c, req)
	})
}
func (a *SeriesAdminApi) SetFeaturedContent(c *gin.Context) {
	var req request.SeriesFeaturedContentReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.SetFeaturedContent(c, req)
	})
}
func (a *SeriesAdminApi) GetList(c *gin.Context) {
	var req request.ListReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.GetList(c, req)
	})
}

func (a *SeriesAdminApi) SetPriority(c *gin.Context) {
	var req request.PriorityReq
	a.processRequest(c, &req, func(r any) any {
		return a.repo.SetPriority(c, req)
	})
}
