package model

import (
	"loop/internal/config"
	"loop/internal/request"
	"loop/pkg/types"

	"github.com/sirupsen/logrus"
	"go4.org/syncutil/singleflight"
)

// 代表一个字幕信息 每个用户的每个视频都会有一个这样的数据，不论是远程视频还是本地 只是SubtitleUrl不一致
// 后续更换字幕的时候只更换SubtitleUrl 还能保持其他数据不影响
type UserSubtitleRelations struct {
	ModelAutoId
	Uid          string                       `gorm:"not null;index:idx_user_watch,priority:1"`
	ResourceId   string                       `gorm:"type:varchar(20)"`
	ResourceType int                          `gorm:"type:tinyint;default:0"` //视频类型 1代表远程的资源 2代表本地资源
	SubtitleUrl  string                       `gorm:"type:text"`              //字幕编辑：当用户编辑了字幕的时候，将最新的字幕上传然后替换 UserCustomSubtitle 表中的字幕地址即可
	Skips        types.VideoTimeIntervalArray `gorm:"type:LONGTEXT;"`         //因为跳过和收藏频繁修改 数量多 每个数量数据少 直接存json
	Collects     types.VideoTimeIntervalArray `gorm:"type:LONGTEXT;"`         //因为跳过和收藏频繁修改 数量多 每个数量数据少 直接存json
}

func (UserSubtitleRelations) TableName() string {
	return "user_subtitle_relations"
}

// UserLocalResource 定义了用户本地的视频资源
type UserLocalResource struct {
	Model
	Uid             string            `gorm:"not null;index:idx_user_watch,priority:1"`
	LocalVideoPaths types.StringArray `gorm:"type:LONGTEXT;"`        //本地的视频路径
	VideoUrl        string            `gorm:"type:text;"`            //对应的视频URL
	FileName        string            `gorm:"type:text;"`            //对应的文件名
	Position        int64             `gorm:"type:bigint;default:0"` // 用户停止观看的位置
}

func (UserLocalResource) TableName() string {
	return "user_local_resources"
}

// 远程视频和用户的一些信息
type UserRemoteResourceRelations struct {
	ModelAutoId
	Uid        string `gorm:"not null;index:idx_user_watch,priority:1" json:"uid"`
	ResourceId string `gorm:"not null"` // 视频资源ID
	Position   int64  `gorm:"type:bigint;default:0"`
}

func (UserRemoteResourceRelations) TableName() string {
	return "user_remote_resource_relations"
}

func NewVideoModel(dbModel *DbModel, userModel *UserModel, resourceModel *ResourceModel, config *config.Config) *VideoModel {
	return &VideoModel{
		DbModel:       dbModel,
		config:        config,
		resourceModel: resourceModel,
		userModel:     userModel,
		sg:            &singleflight.Group{}}
}

type VideoModel struct {
	*DbModel
	userModel     *UserModel
	resourceModel *ResourceModel
	config        *config.Config
	sg            *singleflight.Group
}

func (r *VideoModel) GetUserLocalResourceById(id string) (*UserLocalResource, error) {
	query := UserLocalResource{Model: Model{Id: id}}
	result := UserLocalResource{}
	if found, err := r.GetOne(&result, query); !found {
		if err != nil {
			return nil, err
		}
		return nil, nil
	}
	return &result, nil
}
func (r *VideoModel) GetUserLocalResourceByIds(ids []string) ([]*UserLocalResource, error) {
	result := []*UserLocalResource{}
	err := r.GetList(&result, "id IN ?", ids)
	if err != nil {
		return nil, err
	}
	return result, nil
}
func (r *VideoModel) GetUserLocalResourceByFileName(uid string, filename string) (*UserLocalResource, error) {
	query := UserLocalResource{Uid: uid, FileName: filename}
	result := UserLocalResource{}
	if found, err := r.GetOne(&result, query); !found {
		if err != nil {
			return nil, err
		}
		return nil, nil
	}
	return &result, nil
}

func (r *VideoModel) GetUserLocalResources(uid string) ([]*UserLocalResource, error) {
	query := UserLocalResource{Uid: uid}
	result := []*UserLocalResource{}
	if found, err := r.GetOne(&result, query); !found {
		if err != nil {
			return nil, err
		}
		return nil, nil
	}
	return result, nil
}
func (r *VideoModel) GetOrCreateUserSubtitleRelations(uid string, resourceId string, resourceType int, subtitleUrl string) (*UserSubtitleRelations, error) {
	userCustomSubtitle, err := r.GetUserSubtitleRelations(uid, resourceId, resourceType)
	if err != nil {
		return nil, err
	}
	if userCustomSubtitle == nil {
		logrus.Info("User ", uid, " not found userCustomSubtitle and begin create resourceId=", resourceId, " resourceType=", resourceType, " subtitleUrl=", subtitleUrl)
		userCustomSubtitle, err = r.CreateUserSubtitleRelations(uid, resourceId, resourceType, subtitleUrl)
		if err != nil {
			return nil, err
		}
	}
	if userCustomSubtitle == nil {
		return nil, nil
	}
	return userCustomSubtitle, nil

}

func (r *VideoModel) GetUserSubtitleRelations(uid string, resourceId string, resourceType int) (*UserSubtitleRelations, error) {
	if resourceId == "" {
		return nil, nil
	}
	query := UserSubtitleRelations{Uid: uid, ResourceId: resourceId, ResourceType: resourceType}
	result := UserSubtitleRelations{}
	if found, err := r.GetOne(&result, query); !found {
		if err != nil {
			return nil, err
		}

		if !found {
			return nil, nil
		}
	}
	return &result, nil
}

func (r *VideoModel) CreateUserSubtitleRelations(uid string, resourceId string, resourceType int, subtitleUrl string) (*UserSubtitleRelations, error) {
	if resourceId == "" {
		return nil, nil
	}
	saver := UserSubtitleRelations{Uid: uid, ResourceId: resourceId, ResourceType: resourceType, SubtitleUrl: subtitleUrl}
	err := r.SaveOne(&saver)
	if err != nil {
		return nil, err
	}

	return &saver, nil
}

// 用户第一次进入远程视频的话 需要创建对应的信息
func (r *VideoModel) GetOrCreateRemoteResourceRelations(uid string, req request.VideoDetailReq) (*UserRemoteResourceRelations, *Resource, error) {
	var resource Resource
	var err error
	found, err := r.GetOne(&resource, "id = ?", req.ResourceId)
	if err != nil {
		return nil, nil, err
	}
	if !found {
		logrus.Error("ResourceId ", req.ResourceId, " not found,please check resource is removed")
		return nil, nil, nil
	}
	resourceUser := UserRemoteResourceRelations{}
	found, err = r.GetOne(&resourceUser, UserRemoteResourceRelations{Uid: uid, ResourceId: req.ResourceId})
	if err != nil {
		return nil, nil, err
	}
	var userCustomSubtitle *UserSubtitleRelations
	//这里要分开查  因为如果存在就不需要在查询一次 GetOriginResourceRelation 了 所以不直接调用GetOrCreateUserCustomSubtitle
	// 当用户进入视频的时候，如果没有对应的 UserCustomSubtitle 需要创建 UserCustomSubtitle ，里面的 SubtitleUrl 指向的视频的原始字幕
	userCustomSubtitle, err = r.GetUserSubtitleRelations(uid, req.ResourceId, req.ResourceType)
	if err != nil {
		return nil, nil, err
	}
	if userCustomSubtitle == nil {
		userCustomSubtitle, err = r.CreateUserSubtitleRelations(uid, req.ResourceId, req.ResourceType, "")
		if err != nil {
			return nil, nil, err
		}
		if userCustomSubtitle == nil {
			return nil, nil, nil
		}
	}
	if !found {
		resourceUser = UserRemoteResourceRelations{
			Position:   0,
			Uid:        uid,
			ResourceId: req.ResourceId,
		}
		err = r.SaveOne(&resourceUser)
		if err != nil {
			return nil, nil, err
		}
	}

	err = r.userModel.AddOrUpdateWatchHistory(uid, request.WatchHistoryAddReq{
		ResourceId:   req.ResourceId,
		ResourceType: req.ResourceType,
		Position:     resourceUser.Position,
	})
	if err != nil {
		return nil, nil, err
	}
	return &resourceUser, &resource, err
}
