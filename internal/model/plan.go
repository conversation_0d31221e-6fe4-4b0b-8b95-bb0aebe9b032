package model

import (
	"encoding/json"
	"fmt"
	"loop/internal/config"
	"loop/pkg/dbx"
	"loop/pkg/enum"
	"loop/pkg/timex"
	"loop/pkg/types"
	"time"

	"github.com/sirupsen/logrus"
	"go4.org/syncutil/singleflight"
)

// 数据库字段名常量
const (
	FieldStudyDate        = "study_timestamp"
	FieldTargetSentences  = "target_sentences"
	FieldCurrentSentences = "current_sentences"
	FieldPlanId           = "plan_id"
	FieldWeekId           = "week_id"
	FieldStageId          = "stage_id"
	FieldPlanDayNumber    = "plan_day_number"
	FieldWeekNumber       = "week_number"
	FieldSortOrder        = "sort_order"
	FieldStartTimestamp   = "start_timestamp"
	FieldEndTimestamp     = "end_timestamp"
	FieldStatus           = "status"
	FieldResourceIds      = "resource_ids"
)

// UserQuestionnaire 存储用户的问卷回答
type UserQuestionnaire struct {
	Model
	Uid               string `gorm:"not null;index:idx_user_questionnaire,priority:1"`
	MotivationSource  string `gorm:"type:text;comment:用户的动力来源"`
	DesiredAbility    string `gorm:"type:text;comment:用户想提高的能力"`
	CurrentLevel      string `gorm:"size:10;comment:用户当前级别"`
	TargetLevel       string `gorm:"size:10;comment:用户目标级别"`
	DailyStudyMinutes int    `gorm:"type:int;comment:用户每天学习时间(分钟)"`
}

func (UserQuestionnaire) TableName() string {
	return "user_questionnaires"
}

// LearningPlan 表示用户的学习计划
type LearningPlan struct {
	Model
	Uid             string `gorm:"not null;index:idx_user_plan,priority:1"`
	StartLevel      string `gorm:"size:10;not null;comment:起始级别"`
	TargetLevel     string `gorm:"size:10;not null;comment:目标级别"`
	StartTimestamp  int64  `gorm:"type:bigint;not null;comment:计划开始日期（0点时间戳，毫秒）"`
	EndTimestamp    int64  `gorm:"type:bigint;comment:计划结束日期（0点时间戳，毫秒）"`
	Status          int    `gorm:"type:tinyint;default:1;comment:状态 1进行中 0已作废"`
	QuestionnaireId string `gorm:"size:20;comment:关联的问卷ID"`
	// 学习计划配置
	StudyDaysOfWeek types.IntArray `gorm:"type:json;comment:每周学习的具体日期，1-7代表周一到周日，如[1,2,3,4]表示周一到周四学习"`
	DailySentences  int            `gorm:"type:int;default:10;comment:每天学习句数目标"`
	TotalLearnDays  int            `gorm:"type:int;default:0;comment:计划总学习天数"`
}

func (LearningPlan) TableName() string {
	return "learning_plans"
}

// LearningPlanStage 表示学习计划中的一个阶段
type LearningPlanStage struct {
	Model
	PlanId      string `gorm:"not null;index:idx_plan_stage,priority:1;comment:关联的计划ID"`
	Uid         string `gorm:"not null;index:idx_user_stage,priority:1;comment:用户ID"`
	StageDesc   string `gorm:"size:20;comment:阶段描述，如 A0-1, A1-2 等"`
	Objective   string `gorm:"type:text;comment:阶段目标"`
	SortOrder   int    `gorm:"type:int;default:0;comment:排序顺序"`
	ResourceIds string `gorm:"type:text;comment:资源ID列表，JSON格式"`
}

func (LearningPlanStage) TableName() string {
	return "learning_plan_stages"
}

// LearningPlanWeek 表示学习计划中的一周
type LearningPlanWeek struct {
	Model
	StageId        string `gorm:"not null;index:idx_stage_week,priority:1;comment:关联的阶段ID"`
	PlanId         string `gorm:"not null;index:idx_plan_week,priority:1;comment:关联的计划ID"`
	Uid            string `gorm:"not null;index:idx_user_week,priority:1;comment:用户ID"`
	WeekNumber     int    `gorm:"type:int;not null;comment:周数，从1开始"`
	StartTimestamp int64  `gorm:"type:bigint;not null;comment:周开始日期（0点时间戳，毫秒）"`
	EndTimestamp   int64  `gorm:"type:bigint;not null;comment:周结束日期（0点时间戳，毫秒）"`
}

func (LearningPlanWeek) TableName() string {
	return "learning_plan_weeks"
}

// LearningPlanDay 表示学习计划中的一天
type LearningPlanDay struct {
	Model
	WeekId           string                       `gorm:"not null;index:idx_week_day,priority:1;comment:关联的周ID"`
	StageId          string                       `gorm:"not null;index:idx_stage_day,priority:1;comment:关联的阶段ID"`
	PlanId           string                       `gorm:"not null;index:idx_plan_day,priority:1;comment:关联的计划ID"`
	Uid              string                       `gorm:"not null;index:idx_user_day,priority:1;comment:用户ID"`
	PlanDayNumber    int                          `gorm:"type:int;not null;comment:学习天在整个计划中的顺序，从1开始"`
	WeekDayNumber    int                          `gorm:"type:int;not null;comment:学习天在周中的顺序，从1开始"`
	ResourceIds      string                       `gorm:"type:text;comment:资源ID列表，JSON格式"`
	StudyTimestamp   int64                        `gorm:"type:bigint;not null;comment:实际学习日期（0点时间戳，毫秒）"`
	Status           int                          `gorm:"type:tinyint;default:0;comment:学习状态 0未开始 1进行中 2已完成"`
	CurrentSentences int                          `gorm:"type:int;default:0;comment:当前已学习句数"`
	TargetSentences  int                          `gorm:"type:int;default:0;comment:目标学习句数"`
	RecordedRanges   types.VideoTimeIntervalArray `gorm:"type:text;comment:录音句子时间范围数组，JSON格式，包含开始时间和结束时间"`
}

func (LearningPlanDay) TableName() string {
	return "learning_plan_days"
}

func NewPlanModel(dbModel *DbModel, config *config.Config) *PlanModel {
	return &PlanModel{
		DbModel: dbModel,
		config:  config,
		sg:      &singleflight.Group{},
	}
}

type PlanModel struct {
	*DbModel
	config *config.Config
	sg     *singleflight.Group
}

// LearningProgress 表示学习进度信息
type LearningProgress struct {
	CompletedDays     int       // 已完成的学习天数
	CurrentDayNumber  int       // 当前学习到第几天（从1开始）
	TodayStatus       int       // 今天的学习状态
	TodayDate         time.Time // 今天的日期
	LastCompletedDate time.Time // 最后完成学习的日期
}

// GetUserQuestionnaire 获取用户的问卷信息
func (p *PlanModel) GetUserQuestionnaire(uid string) (*UserQuestionnaire, error) {
	res, err := p.sg.Do(fmt.Sprintf("get_user_questionnaire_%s", uid), func() (any, error) {
		query := UserQuestionnaire{Uid: uid}
		questionnaire := UserQuestionnaire{}
		found, err := p.GetOne(&questionnaire, query)
		if err != nil {
			return nil, err
		}
		if !found {
			return nil, nil
		}
		return &questionnaire, nil
	})
	if err != nil {
		return nil, err
	}
	if res == nil {
		return nil, nil
	}
	data, ok := res.(*UserQuestionnaire)
	if !ok {
		return nil, fmt.Errorf("type assertion to *UserQuestionnaire failed")
	}
	return data, nil
}

// GetActiveLearningPlan 获取用户当前活跃的学习计划
func (p *PlanModel) GetActiveLearningPlan(uid string) (*LearningPlan, error) {
	res, err := p.sg.Do(fmt.Sprintf("get_active_learning_plan_%s", uid), func() (any, error) {
		query := LearningPlan{Uid: uid, Status: 1}
		plan := LearningPlan{}
		found, err := p.GetOne(&plan, query)
		if err != nil {
			return nil, err
		}
		if !found {
			return nil, nil
		}
		return &plan, nil
	})
	if err != nil {
		return nil, err
	}
	if res == nil {
		return nil, nil
	}
	data, ok := res.(*LearningPlan)
	if !ok {
		return nil, fmt.Errorf("type assertion to *LearningPlan failed")
	}
	return data, nil
}

// GetPlanStages 获取学习计划的所有阶段
func (p *PlanModel) GetPlanStages(planId string) ([]*LearningPlanStage, error) {
	var stages []*LearningPlanStage
	err := p.GetList(&stages, FieldPlanId+" = ? ORDER BY "+FieldSortOrder+" ASC", planId)
	if err != nil {
		return nil, err
	}
	return stages, nil
}

// InvalidateUserPlans 将用户所有现有计划标记为无效
func (p *PlanModel) InvalidateUserPlans(uid string) error {
	return p.Tx(func(txDb *dbx.DBExtension) error {
		if err := txDb.Model(&LearningPlan{}).Where("uid = ? AND status = ?", uid, 1).Update("status", 0).Error; err != nil {
			return err
		}
		return nil
	})
}

// GetFirstFeaturedResource 获取第一个精选资源，如果没有则获取第一个普通资源
func (p *PlanModel) GetFirstFeaturedResource(targetLangCode string) (*Resource, error) {
	// 首先尝试获取精选资源
	var featuredContents []*FeaturedContent
	err := p.GetList(&featuredContents, "content_type = ? AND lang_code = ?", int(enum.Resource), targetLangCode)
	if err != nil {
		return nil, err
	}

	// 如果有精选资源，返回第一个
	if len(featuredContents) > 0 {
		resource := &Resource{}
		found, err := p.GetOne(resource, Resource{Model: Model{Id: featuredContents[0].ContentID}})
		if err != nil {
			return nil, err
		}
		if found {
			return resource, nil
		}
	}

	// 如果没有精选资源，获取第一个普通资源
	var resources []*Resource
	err = p.GetList(&resources, "")
	if err != nil {
		return nil, err
	}

	if len(resources) > 0 {
		return resources[0], nil
	}

	return nil, fmt.Errorf("no resources found")
}

// GetPlanWeeks 获取学习计划阶段的所有周
func (p *PlanModel) GetPlanWeeks(stageId string) ([]*LearningPlanWeek, error) {
	var weeks []*LearningPlanWeek
	err := p.GetList(&weeks, FieldStageId+" = ? ORDER BY "+FieldWeekNumber+" ASC", stageId)
	if err != nil {
		return nil, err
	}
	return weeks, nil
}

// GetPlanAllWeeks 获取学习计划的所有周（直接通过planId查询）
func (p *PlanModel) GetPlanAllWeeks(planId string) ([]*LearningPlanWeek, error) {
	var weeks []*LearningPlanWeek
	err := p.GetList(&weeks, FieldPlanId+" = ? ORDER BY "+FieldWeekNumber+" ASC", planId)
	if err != nil {
		return nil, err
	}
	return weeks, nil
}

// GetUserPlanDaysByDateRange 根据日期范围获取用户学习计划的天数据
func (p *PlanModel) GetUserPlanDaysByDateRange(uid string, planId string, startDateTimestamp, endDateTimestamp int64) ([]*LearningPlanDay, error) {
	var days []*LearningPlanDay
	err := p.GetList(&days, "uid = ? AND "+FieldPlanId+" = ? AND "+FieldStudyDate+" >= ? AND "+FieldStudyDate+" <= ? ORDER BY "+FieldStudyDate+" ASC",
		uid, planId, startDateTimestamp, endDateTimestamp)
	if err != nil {
		return nil, err
	}
	return days, nil
}

// GetWeeksDays 批量获取多个周的所有天数据
func (p *PlanModel) GetWeeksDays(weekIds []string) ([]*LearningPlanDay, error) {
	if len(weekIds) == 0 {
		return []*LearningPlanDay{}, nil
	}

	var days []*LearningPlanDay
	err := p.GetList(&days, FieldWeekId+" IN (?) ORDER BY "+FieldWeekId+", "+FieldPlanDayNumber+" ASC", weekIds)
	if err != nil {
		return nil, err
	}
	return days, nil
}

// UpdateStudyDaysAndReschedule 统一处理学习日期配置更新和日程重新安排
// 优化版本：避免重复逻辑，一次性完成配置更新和日程重排
func (p *PlanModel) UpdateStudyDaysAndReschedule(planId string, studyDaysOfWeek []int) error {
	logrus.Infof("[MODEL] UpdateStudyDaysAndReschedule 开始处理, plan_id=%s, 新学习日期=%v", planId, studyDaysOfWeek)
	return p.Tx(func(txDb *dbx.DBExtension) error {
		// 1. 获取计划信息
		logrus.Infof("[MODEL] UpdateStudyDaysAndReschedule 获取计划信息...")
		plan := &LearningPlan{Model: Model{Id: planId}}
		found, err := p.GetOne(plan, *plan)
		if err != nil {
			logrus.WithError(err).Error("[MODEL] UpdateStudyDaysAndReschedule 获取计划失败")
			return err
		}
		if !found {
			logrus.Errorf("[MODEL] UpdateStudyDaysAndReschedule 计划未找到: %s", planId)
			return fmt.Errorf("learning plan not found: %s", planId)
		}

		logrus.Infof("[MODEL] UpdateStudyDaysAndReschedule 找到计划, 当前学习日期=%v", plan.StudyDaysOfWeek)

		// 2. 更新计划的学习日期配置
		logrus.Infof("[MODEL] UpdateStudyDaysAndReschedule 更新计划学习日期配置...")
		plan.StudyDaysOfWeek = studyDaysOfWeek
		if err := txDb.Save(plan).Error; err != nil {
			logrus.WithError(err).Error("[MODEL] UpdateStudyDaysAndReschedule 更新计划学习日期配置失败")
			return fmt.Errorf("更新计划学习日期配置失败: %w", err)
		}

		// 3. 获取当前时间
		now := timex.Now()
		today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
		logrus.Infof("[MODEL] UpdateStudyDaysAndReschedule 当前时间: %s, today日期: %s",
			now.Format("2006-01-02 15:04:05"), today.Format("2006-01-02"))

		// 4. 获取所有学习天数据，按计划天数排序
		logrus.Infof("[MODEL] UpdateStudyDaysAndReschedule 获取所有学习天数据...")
		var allDays []*LearningPlanDay
		err = txDb.Where(FieldPlanId+" = ?", planId).Order(FieldPlanDayNumber + " ASC").Find(&allDays).Error
		if err != nil {
			logrus.WithError(err).Error("[MODEL] UpdateStudyDaysAndReschedule 获取计划学习天失败")
			return fmt.Errorf("获取计划学习天失败: %w", err)
		}

		logrus.Infof("[MODEL] UpdateStudyDaysAndReschedule 找到学习天数量: %d", len(allDays))
		if len(allDays) == 0 {
			logrus.Infof("[MODEL] UpdateStudyDaysAndReschedule 没有学习天，直接返回")
			return nil // 没有学习天，无需处理
		}

		// 5. 分类处理学习天：保留已完成和进行中的天，只重新安排未开始的天
		logrus.Infof("[MODEL] UpdateStudyDaysAndReschedule 开始分类处理学习天...")
		var completedDays []*LearningPlanDay
		var daysToDelete []string
		var firstNotStartedIndex = -1

		for i, day := range allDays {
			dayDate := timex.TimestampToTime(day.StudyTimestamp)
			dayDateStr := dayDate.Format("2006-01-02")

			if day.Status == int(enum.PlanDayStatusCompleted) { // 已完成的天
				completedDays = append(completedDays, day)
				logrus.Infof("[MODEL] UpdateStudyDaysAndReschedule 保留已完成天: 计划天%d, 日期=%s",
					day.PlanDayNumber, dayDateStr)
			} else if day.Status == int(enum.PlanDayStatusInProgress) { // 进行中的天
				// 进行中的天作为历史数据保留，不删除
				logrus.Infof("[MODEL] UpdateStudyDaysAndReschedule 保留进行中天: 计划天%d, 日期=%s (历史数据)",
					day.PlanDayNumber, dayDateStr)
			} else if day.Status == int(enum.PlanDayStatusNotStarted) { // 未开始的天
				// 只有未开始的天才进行重新安排
				if firstNotStartedIndex == -1 {
					firstNotStartedIndex = i
				}
				daysToDelete = append(daysToDelete, day.Id)
				logrus.Infof("[MODEL] UpdateStudyDaysAndReschedule 标记删除未开始天: 计划天%d, 日期=%s (未开始，需要重新安排)",
					day.PlanDayNumber, dayDateStr)
			}
		}

		logrus.Infof("[MODEL] UpdateStudyDaysAndReschedule 分类结果: 已完成天数=%d, 待删除未开始天数=%d",
			len(completedDays), len(daysToDelete))

		// 6. 删除需要重新安排的未开始天
		if len(daysToDelete) > 0 {
			logrus.Infof("[MODEL] UpdateStudyDaysAndReschedule 删除需要重新安排的未开始天, 数量=%d", len(daysToDelete))
			if err := txDb.Where("id IN ?", daysToDelete).Delete(&LearningPlanDay{}).Error; err != nil {
				logrus.WithError(err).Error("[MODEL] UpdateStudyDaysAndReschedule 删除学习天失败")
				return fmt.Errorf("删除需要重新安排的学习天失败: %w", err)
			}
		} else {
			logrus.Infof("[MODEL] UpdateStudyDaysAndReschedule 没有需要删除的未开始学习天")
		}

		// 7. 清理空的周数据
		logrus.Infof("[MODEL] UpdateStudyDaysAndReschedule 清理空的周数据...")
		if err := p.cleanupEmptyWeeks(txDb, planId); err != nil {
			logrus.WithError(err).Error("[MODEL] UpdateStudyDaysAndReschedule 清理空周数据失败")
			return fmt.Errorf("清理空周数据失败: %w", err)
		}

		// 8. 计算剩余需要安排的天数
		// 统计已完成和进行中的天数（这些都不需要重新安排）
		var inProgressDaysCount int
		for _, day := range allDays {
			if day.Status == int(enum.PlanDayStatusInProgress) {
				inProgressDaysCount++
			}
		}
		completedDaysCount := len(completedDays)
		totalLearnDays := plan.TotalLearnDays
		if totalLearnDays == 0 {
			totalLearnDays = 28 // 默认28天
		}

		// 剩余天数 = 总学习天数 - 已完成天数 - 进行中天数
		remainingDays := totalLearnDays - completedDaysCount - inProgressDaysCount
		logrus.Infof("[MODEL] UpdateStudyDaysAndReschedule 计算剩余天数: 总学习天数=%d, 已完成天数=%d, 进行中天数=%d, 剩余天数=%d",
			totalLearnDays, completedDaysCount, inProgressDaysCount, remainingDays)

		if remainingDays <= 0 {
			logrus.Infof("[MODEL] UpdateStudyDaysAndReschedule 已完成所有学习天数，直接返回")
			return nil // 已经完成所有学习天数
		}

		// 9. 确定重新安排的起始日期
		var startDateTimestamp int64

		// 找到最后一个已完成或进行中的天
		var lastActiveDay *LearningPlanDay
		for _, day := range allDays {
			if day.Status == int(enum.PlanDayStatusCompleted) || day.Status == int(enum.PlanDayStatusInProgress) {
				if lastActiveDay == nil || day.PlanDayNumber > lastActiveDay.PlanDayNumber {
					lastActiveDay = day
				}
			}
		}

		if lastActiveDay != nil {
			// 从最后一个已完成或进行中天的下一天开始
			lastActiveDate := timex.TimestampToTime(lastActiveDay.StudyTimestamp)
			nextDay := lastActiveDate.AddDate(0, 0, 1)
			if nextDay.Before(today) {
				nextDay = today
			}
			startDateTimestamp = timex.GetDateZeroTimestamp(nextDay)
			logrus.Infof("[MODEL] UpdateStudyDaysAndReschedule 从最后活跃天的下一天开始: 最后活跃日期=%s, 下一天=%s, 起始日期=%s",
				lastActiveDate.Format("2006-01-02"), nextDay.Format("2006-01-02"),
				timex.TimestampToTime(startDateTimestamp).Format("2006-01-02"))
		} else {
			// 没有已完成或进行中的天，从今天开始
			startDateTimestamp = timex.GetDateZeroTimestamp(today)
			logrus.Infof("[MODEL] UpdateStudyDaysAndReschedule 没有活跃天，从今天开始: 起始日期=%s",
				today.Format("2006-01-02"))
		}

		// 10. 重新生成剩余学习天数据
		logrus.Infof("[MODEL] UpdateStudyDaysAndReschedule 开始重新生成学习天数据, 起始日期=%s, 剩余天数=%d",
			timex.TimestampToTime(startDateTimestamp).Format("2006-01-02"), remainingDays)
		return p.generateLearningDaysFromDate(txDb, plan, studyDaysOfWeek, startDateTimestamp, remainingDays)
	})
}

// cleanupEmptyWeeks 清理没有学习天的空周数据
func (p *PlanModel) cleanupEmptyWeeks(txDb *dbx.DBExtension, planId string) error {
	// 查找没有关联学习天的周
	var emptyWeekIds []string
	err := txDb.Raw(`
		SELECT w.id 
		FROM learning_plan_weeks w 
		LEFT JOIN learning_plan_days d ON w.id = d.`+FieldWeekId+` 
		WHERE w.`+FieldPlanId+` = ? AND d.id IS NULL
	`, planId).Scan(&emptyWeekIds).Error

	if err != nil {
		return err
	}

	// 删除空周
	if len(emptyWeekIds) > 0 {
		if err := txDb.Where("id IN ?", emptyWeekIds).Delete(&LearningPlanWeek{}).Error; err != nil {
			return fmt.Errorf("删除空周数据失败: %w", err)
		}
		logrus.Infof("清理了 %d 个空周数据", len(emptyWeekIds))
	}

	return nil
}

// generateLearningDaysFromDate 从指定日期开始生成学习天数据
func (p *PlanModel) generateLearningDaysFromDate(txDb *dbx.DBExtension, plan *LearningPlan, studyDaysOfWeek []int, startDateTimestamp int64, totalDays int) error {
	// 获取或创建阶段信息
	var stage *LearningPlanStage
	stages, err := p.GetPlanStages(plan.Id)
	if err != nil {
		return fmt.Errorf("failed to get plan stages: %w", err)
	}

	if len(stages) > 0 {
		stage = stages[0] // 使用第一个阶段
	} else {
		// 如果没有阶段，创建一个默认阶段
		stage = &LearningPlanStage{
			PlanId:      plan.Id,
			Uid:         plan.Uid,
			StageDesc:   "默认学习阶段",
			Objective:   "完成学习目标",
			SortOrder:   1,
			ResourceIds: "[]",
		}
		if err := txDb.Create(stage).Error; err != nil {
			return fmt.Errorf("failed to create default stage: %w", err)
		}
	}

	// 生成学习天数据
	currentDate := timex.TimestampToTime(startDateTimestamp)
	generatedDays := 0

	// 查询当前计划中已存在的最大WeekNumber，避免重复
	var maxWeekNumber int
	err = txDb.Model(&LearningPlanWeek{}).Where(FieldPlanId+" = ?", plan.Id).Select("COALESCE(MAX(" + FieldWeekNumber + "), 0)").Scan(&maxWeekNumber).Error
	if err != nil {
		return fmt.Errorf("获取最大周数失败: %w", err)
	}
	weekNumber := maxWeekNumber + 1 // 从下一个周数开始

	var currentWeek *LearningPlanWeek
	weekDayCounter := 1 // 用于跟踪当前周内的学习天顺序

	for generatedDays < totalDays {
		// 获取当前日期是周几（1=周一，7=周日）
		weekday := int(currentDate.Weekday())
		if weekday == 0 {
			weekday = 7 // 将周日从0改为7
		}

		// 检查今天是否是学习日
		isStudyDay := false
		for _, day := range studyDaysOfWeek {
			if day == weekday {
				isStudyDay = true
				break
			}
		}

		if isStudyDay {
			logrus.Infof("[MODEL] generateLearningDaysFromDate 当前日期=%s 是学习日, 周几=%d",
				currentDate.Format("2006-01-02"), weekday)

			// 如果需要新的周数据或者当前周为空
			if currentWeek == nil || !p.isDateInWeek(currentDate, currentWeek) {
				logrus.Infof("[MODEL] generateLearningDaysFromDate 需要创建或获取新周, 当前日期=%s, 周数=%d",
					currentDate.Format("2006-01-02"), weekNumber)
				currentWeek, err = p.getOrCreateWeekForDateOptimized(txDb, plan, stage, currentDate, weekNumber, studyDaysOfWeek)
				if err != nil {
					logrus.WithError(err).Error("[MODEL] generateLearningDaysFromDate 创建或获取周失败")
					return fmt.Errorf("failed to get or create week: %w", err)
				}
				logrus.Infof("[MODEL] generateLearningDaysFromDate 成功获取周, week_id=%s, week_number=%d",
					currentWeek.Id, currentWeek.WeekNumber)
				weekNumber++
				weekDayCounter = 1 // 新周开始，重置周内计数器
			}

			// 创建学习天数据
			day := &LearningPlanDay{
				WeekId:           currentWeek.Id,
				StageId:          stage.Id,
				PlanId:           plan.Id,
				Uid:              plan.Uid,
				PlanDayNumber:    generatedDays + 1, // 学习天在整个计划中的顺序
				WeekDayNumber:    weekDayCounter,    // 在周中的顺序，从1开始
				StudyTimestamp:   timex.GetDateZeroTimestamp(currentDate),
				Status:           0, // 未开始
				CurrentSentences: 0,
				TargetSentences:  plan.DailySentences,
				ResourceIds:      stage.ResourceIds,
			}

			if err := txDb.Create(day).Error; err != nil {
				logrus.WithError(err).Error("[MODEL] generateLearningDaysFromDate 创建学习天失败")
				return fmt.Errorf("failed to create learning day: %w", err)
			}

			logrus.Infof("[MODEL] generateLearningDaysFromDate 成功创建学习天: 计划天%d, 日期=%s, week_id=%s",
				day.PlanDayNumber, currentDate.Format("2006-01-02"), currentWeek.Id)

			generatedDays++
			weekDayCounter++ // 增加周内计数器
		}

		// 移动到下一天
		currentDate = currentDate.AddDate(0, 0, 1)
	}

	return nil
}

// getOrCreateWeekForDateOptimized 优化版本的获取或创建周数据
func (p *PlanModel) getOrCreateWeekForDateOptimized(txDb *dbx.DBExtension, plan *LearningPlan, stage *LearningPlanStage, date time.Time, weekNumber int, studyDaysOfWeek []int) (*LearningPlanWeek, error) {
	// 计算周的开始和结束日期
	weekday := int(date.Weekday())
	if weekday == 0 {
		weekday = 7 // 将周日从0改为7
	}

	// 计算本周一的日期
	daysFromMonday := weekday - 1
	weekStart := date.AddDate(0, 0, -daysFromMonday)
	weekEnd := weekStart.AddDate(0, 0, 6)

	// 尝试查找现有的周数据
	var existingWeek LearningPlanWeek
	dateTimestamp := timex.GetDateZeroTimestamp(date)
	err := txDb.Where(FieldPlanId+" = ? AND "+FieldStartTimestamp+" <= ? AND "+FieldEndTimestamp+" >= ?", plan.Id, dateTimestamp, dateTimestamp).First(&existingWeek).Error
	if err == nil {
		logrus.Infof("找到现有周数据: week_number=%d, start_date=%d, end_date=%d, 查询日期=%d",
			existingWeek.WeekNumber, existingWeek.StartTimestamp, existingWeek.EndTimestamp, dateTimestamp)
		// 更新现有周的StudyDaysOfWeek配置
		if err := txDb.Save(&existingWeek).Error; err != nil {
			return nil, fmt.Errorf("failed to update existing week: %w", err)
		}
		return &existingWeek, nil
	} else {
		logrus.Infof("未找到现有周数据，将创建新周: plan_id=%s, 查询日期=%d, 错误=%v", plan.Id, dateTimestamp, err)
	}

	// 创建新的周数据
	week := &LearningPlanWeek{
		StageId:        stage.Id,
		PlanId:         plan.Id,
		Uid:            plan.Uid,
		WeekNumber:     weekNumber,
		StartTimestamp: timex.GetDateZeroTimestamp(weekStart),
		EndTimestamp:   timex.GetDateZeroTimestamp(weekEnd),
	}

	if err := txDb.Create(week).Error; err != nil {
		return nil, fmt.Errorf("failed to create week: %w", err)
	}

	return week, nil
}

// GetPlanAllDays 获取计划的所有天数据（按时间排序）
func (p *PlanModel) GetPlanAllDays(planId string) ([]*LearningPlanDay, error) {
	var days []*LearningPlanDay
	err := p.GetList(&days, FieldPlanId+" = ? ORDER BY "+FieldStudyDate+" ASC", planId)
	if err != nil {
		return nil, err
	}
	return days, nil
}

// DeleteWeekDays 删除指定周的所有天数据
func (p *PlanModel) DeleteWeekDays(weekId string) error {
	return p.Tx(func(txDb *dbx.DBExtension) error {
		return txDb.Where(FieldWeekId+" = ?", weekId).Delete(&LearningPlanDay{}).Error
	})
}

// DeleteWeek 删除指定的周数据
func (p *PlanModel) DeleteWeek(weekId string) error {
	return p.Tx(func(txDb *dbx.DBExtension) error {
		// 先删除周下的所有天数据
		if err := p.DeleteWeekDays(weekId); err != nil {
			return err
		}
		// 再删除周数据
		return txDb.Where("id = ?", weekId).Delete(&LearningPlanWeek{}).Error
	})
}

// UpdateWeekStudyDays 更新周的学习天数配置
func (p *PlanModel) UpdateWeekStudyDays(weekId string, studyDaysOfWeek []int) error {
	return p.Tx(func(txDb *dbx.DBExtension) error {
		// 获取周信息
		week := &LearningPlanWeek{Model: Model{Id: weekId}}
		found, err := p.GetOne(week, *week)
		if err != nil {
			return err
		}
		if !found {
			return fmt.Errorf("learning plan week not found: %s", weekId)
		}

		if err := txDb.Save(week).Error; err != nil {
			return err
		}

		// 获取今天的时间
		todayStartTimestamp := timex.GetTodayZeroTimestamp()

		// 删除今天及以后的学习天数据
		if err := txDb.Where(FieldWeekId+" = ? AND "+FieldStudyDate+" >= ?", weekId, todayStartTimestamp).Delete(&LearningPlanDay{}).Error; err != nil {
			return err
		}

		// 重新生成今天及以后的学习天数据
		return p.regenerateWeekLearningDays(txDb, week, todayStartTimestamp, studyDaysOfWeek)
	})
}

// regenerateWeekLearningDays 重新生成周内今天及以后的学习天数据
func (p *PlanModel) regenerateWeekLearningDays(txDb *dbx.DBExtension, week *LearningPlanWeek, startDateTimestamp int64, studyDaysOfWeek []int) error {
	weekStartTime := timex.TimestampToTime(week.StartTimestamp)
	startTime := timex.TimestampToTime(startDateTimestamp)

	// 为每周的学习日生成天数据
	for dayIndex, dayOfWeek := range studyDaysOfWeek {
		// 计算实际的学习日期
		studyDate := weekStartTime
		for i := 1; i < dayOfWeek; i++ {
			studyDate = studyDate.AddDate(0, 0, 1)
		}

		// 只生成今天及以后的学习天
		if studyDate.Before(startTime) {
			continue
		}

		day := &LearningPlanDay{
			WeekId:           week.Id,
			PlanId:           week.PlanId,
			Uid:              week.Uid,
			PlanDayNumber:    dayIndex + 1,
			WeekDayNumber:    dayIndex + 1,
			StudyTimestamp:   timex.GetDateZeroTimestamp(studyDate),
			Status:           0, // 未开始
			CurrentSentences: 0,
			TargetSentences:  10, // 默认值，实际应该从计划中获取
		}

		if err := txDb.Create(day).Error; err != nil {
			return err
		}
	}

	return nil
}

// getCurrentLearningProgress 获取当前学习进度
func (p *PlanModel) getCurrentLearningProgress(txDb *dbx.DBExtension, planId string) (*LearningProgress, error) {
	today := timex.Now()
	todayStart := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, today.Location())

	// 获取所有学习天数据，按学习日期排序
	var allDays []*LearningPlanDay
	err := txDb.Where(FieldPlanId+" = ?", planId).Order(FieldStudyDate + " ASC").Find(&allDays).Error
	if err != nil {
		return nil, err
	}

	progress := &LearningProgress{
		TodayDate: todayStart,
	}

	// 计算已完成的天数和当前学习进度
	completedCount := 0
	currentDayNumber := 1
	todayStatus := 0

	for i, day := range allDays {
		dayDate := timex.TimestampToTime(day.StudyTimestamp)

		if day.Status == int(enum.PlanDayStatusCompleted) { // 已完成
			completedCount++
			progress.LastCompletedDate = dayDate
		}

		// 找到今天的学习状态
		if dayDate.Equal(todayStart) {
			todayStatus = day.Status
			currentDayNumber = i + 1
		} else if dayDate.Before(todayStart) && day.Status != int(enum.PlanDayStatusCompleted) {
			// 如果是过去的日期但未完成，当前学习天数应该是这一天
			currentDayNumber = i + 1
		} else if dayDate.After(todayStart) && currentDayNumber == 1 {
			// 如果今天没有学习计划，当前学习天数是下一个学习日
			currentDayNumber = i + 1
		}
	}

	progress.CompletedDays = completedCount
	progress.CurrentDayNumber = currentDayNumber
	progress.TodayStatus = todayStatus

	return progress, nil
}

// isDateInWeek 检查日期是否在指定周内
func (p *PlanModel) isDateInWeek(date time.Time, week *LearningPlanWeek) bool {
	dateOnly := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
	startDate := timex.TimestampToTime(week.StartTimestamp)
	endDate := timex.TimestampToTime(week.EndTimestamp)

	return (dateOnly.Equal(startDate) || dateOnly.After(startDate)) && (dateOnly.Equal(endDate) || dateOnly.Before(endDate))
}

// getOrCreateWeekForDate 获取或创建指定日期所在的周
func (p *PlanModel) getOrCreateWeekForDate(txDb *dbx.DBExtension, plan *LearningPlan, stage *LearningPlanStage, date time.Time, weekNumber int, studyDaysOfWeek []int) (*LearningPlanWeek, error) {
	// 计算周的开始和结束日期
	weekday := int(date.Weekday())
	if weekday == 0 {
		weekday = 7 // 将周日从0改为7
	}

	// 计算本周一的日期
	daysFromMonday := weekday - 1
	weekStart := date.AddDate(0, 0, -daysFromMonday)
	weekEnd := weekStart.AddDate(0, 0, 6)

	// 尝试查找现有的周数据
	var existingWeek LearningPlanWeek
	dateTimestamp := timex.GetDateZeroTimestamp(date)
	err := txDb.Where(FieldPlanId+" = ? AND "+FieldStartTimestamp+" <= ? AND "+FieldEndTimestamp+" >= ?", plan.Id, dateTimestamp, dateTimestamp).First(&existingWeek).Error
	if err == nil {
		return &existingWeek, nil
	}

	// 创建新的周数据
	week := &LearningPlanWeek{
		StageId:        stage.Id,
		PlanId:         plan.Id,
		Uid:            plan.Uid,
		WeekNumber:     weekNumber,
		StartTimestamp: timex.GetDateZeroTimestamp(weekStart),
		EndTimestamp:   timex.GetDateZeroTimestamp(weekEnd),
	}

	if err := txDb.Create(week).Error; err != nil {
		return nil, fmt.Errorf("failed to create week: %w", err)
	}

	return week, nil
}

// GetLearningProgress 获取学习进度信息
func (p *PlanModel) GetLearningProgress(planId string) (*LearningProgress, error) {
	var progress *LearningProgress
	err := p.Tx(func(txDb *dbx.DBExtension) error {
		var err error
		progress, err = p.getCurrentLearningProgress(txDb, planId)
		return err
	})
	return progress, err
}

// ValidateStudyDaysOfWeek 验证学习日期配置的有效性
func (p *PlanModel) ValidateStudyDaysOfWeek(studyDaysOfWeek []int) error {
	if len(studyDaysOfWeek) == 0 {
		return fmt.Errorf("学习日期配置不能为空")
	}

	// 检查是否有重复的日期
	seen := make(map[int]bool)
	for _, day := range studyDaysOfWeek {
		if day < 1 || day > 7 {
			return fmt.Errorf("学习日期必须在1-7之间（1代表周一，7代表周日），当前值：%d", day)
		}
		if seen[day] {
			return fmt.Errorf("学习日期不能重复，重复值：%d", day)
		}
		seen[day] = true
	}

	return nil
}

// GetCurrentStudyDay 获取当前应该学习的天
func (p *PlanModel) GetCurrentStudyDay(planId string) (*LearningPlanDay, error) {
	todayTimestamp := timex.GetTodayZeroTimestamp()

	// 获取今天的学习天数据
	day := &LearningPlanDay{}
	found, err := p.GetOne(day, LearningPlanDay{
		PlanId:         planId,
		StudyTimestamp: todayTimestamp,
	})
	if err != nil {
		return nil, err
	}
	if !found {
		return nil, nil
	}
	return day, nil
}

// GetNextStudyDay 获取下一个学习天
func (p *PlanModel) GetNextStudyDay(planId string) (*LearningPlanDay, error) {
	todayTimestamp := timex.GetTodayZeroTimestamp()

	// 获取下一个学习天数据
	var nextDay LearningPlanDay
	err := p.GetList(&[]*LearningPlanDay{&nextDay}, FieldPlanId+" = ? AND "+FieldStudyDate+" > ? ORDER BY "+FieldStudyDate+" ASC LIMIT 1", planId, todayTimestamp)
	if err != nil {
		return nil, err
	}

	if len([]*LearningPlanDay{&nextDay}) == 0 {
		return nil, nil
	}

	return &nextDay, nil
}

// UpdateLearningPlanDailySentences 更新学习计划的每日句数，并修改后续天的目标句数
func (p *PlanModel) UpdateLearningPlanDailySentences(planId string, dailySentences int) error {
	return p.Tx(func(txDb *dbx.DBExtension) error {
		// 1. 获取计划信息
		plan := &LearningPlan{Model: Model{Id: planId}}
		found, err := p.GetOne(plan, *plan)
		if err != nil {
			return err
		}
		if !found {
			return fmt.Errorf("learning plan not found: %s", planId)
		}

		// 2. 更新计划的每日句数
		plan.DailySentences = dailySentences
		if err := txDb.Save(plan).Error; err != nil {
			return fmt.Errorf("failed to update learning plan daily sentences: %w", err)
		}

		// 3. 获取当前时间戳（今天0点）
		todayTimestamp := timex.GetTodayZeroTimestamp()

		// 4. 批量更新后续天的目标句数（今天及以后的天，但跳过状态为2已完成的天）
		if err := txDb.Model(&LearningPlanDay{}).
			Where(FieldPlanId+" = ? AND "+FieldStudyDate+" >= ? AND "+FieldStatus+" != ?", planId, todayTimestamp, enum.PlanDayStatusCompleted).
			Update(FieldTargetSentences, dailySentences).Error; err != nil {
			return fmt.Errorf("failed to update day target sentences: %w", err)
		}

		return nil
	})
}

// UpdateLearningPlanResource 更新学习计划的资源
// 只处理今天及之后的天，且今天没有已完成的天
func (p *PlanModel) UpdateLearningPlanResource(planId string, resourceId string) error {
	return p.Tx(func(txDb *dbx.DBExtension) error {
		// 1. 获取计划信息
		plan := &LearningPlan{Model: Model{Id: planId}}
		found, err := p.GetOne(plan, *plan)
		if err != nil {
			return err
		}
		if !found {
			return fmt.Errorf("学习计划未找到: %s", planId)
		}

		// 2. 验证资源是否存在
		resource := &Resource{Model: Model{Id: resourceId}}
		found, err = p.GetOne(resource, *resource)
		if err != nil {
			return err
		}
		if !found {
			return fmt.Errorf("资源未找到: %s", resourceId)
		}

		// 3. 获取当前时间并转换为时间戳
		now := timex.Now()
		today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
		todayTimestamp := timex.GetDateZeroTimestamp(today)

		// 4. 将新资源ID序列化为JSON
		newResourceIdsJSON, err := json.Marshal([]string{resourceId})
		if err != nil {
			return fmt.Errorf("序列化资源ID失败: %w", err)
		}

		// 5. 查询需要更新的天数
		var count int64
		query := txDb.Model(&LearningPlanDay{}).
			Where(&LearningPlanDay{PlanId: planId}).
			Where("study_timestamp >= ?", todayTimestamp).
			Not(&LearningPlanDay{Status: 2})

		if err := query.Count(&count).Error; err != nil {
			return fmt.Errorf("查询需要更新的天数失败: %w", err)
		}

		logrus.Infof("准备更新计划 %s 的资源，将影响 %d 个未完成的学习天", planId, count)

		// 6. 更新今天及之后的天（未完成状态的天）
		result := txDb.Model(&LearningPlanDay{}).
			Where(&LearningPlanDay{PlanId: planId}).
			Where("study_timestamp >= ?", todayTimestamp).
			Not(&LearningPlanDay{Status: 2}).
			Update("resource_ids", string(newResourceIdsJSON))

		if result.Error != nil {
			return fmt.Errorf("更新学习天资源失败: %w", result.Error)
		}

		logrus.Infof("成功更新计划 %s 的资源，实际更新了 %d 个学习天", planId, result.RowsAffected)

		return nil
	})
}

// GetDayRecordedRanges 根据学习天ID获取录音句子时间范围数组
func (p *PlanModel) GetDayRecordedRanges(dayId string) (*LearningPlanDay, error) {
	day := &LearningPlanDay{Model: Model{Id: dayId}}
	found, err := p.GetOne(day, *day)
	if err != nil {
		return nil, err
	}
	if !found {
		return nil, fmt.Errorf("学习天未找到: %s", dayId)
	}
	return day, nil
}

// UpdateDayRecordedRanges 更新学习天的录音句子时间范围并自动更新状态
// 返回更新后的完整录音句子时间范围数组
func (p *PlanModel) UpdateDayRecordedRanges(dayId string, recordedRange types.VideoTimeInterval) (types.VideoTimeIntervalArray, error) {
	var updatedRanges types.VideoTimeIntervalArray
	err := p.Tx(func(txDb *dbx.DBExtension) error {
		day := &LearningPlanDay{Model: Model{Id: dayId}}
		found, err := p.GetOne(day, *day)
		if err != nil {
			return err
		}
		if !found {
			return fmt.Errorf("学习天未找到: %s", dayId)
		}

		// 获取原来的录音句子时间范围
		originalRanges := day.RecordedRanges

		// 检查新的范围是否已存在，如果不存在则添加
		exists := false
		for _, existingRange := range originalRanges {
			if recordedRange.Start == existingRange.Start && recordedRange.End == existingRange.End {
				exists = true
				break
			}
		}
		if !exists {
			originalRanges = append(originalRanges, recordedRange)
		}

		day.CurrentSentences = len(originalRanges)
		day.RecordedRanges = originalRanges

		// 更新状态
		if len(originalRanges) == 0 {
			day.Status = int(enum.PlanDayStatusNotStarted)
		} else if len(originalRanges) >= day.TargetSentences {
			day.Status = int(enum.PlanDayStatusCompleted)
		} else {
			day.Status = int(enum.PlanDayStatusInProgress)
		}

		if err := txDb.Save(day).Error; err != nil {
			return err
		}

		// 设置返回的更新后范围
		updatedRanges = originalRanges
		return nil
	})

	return updatedRanges, err
}

// UpdatePlanDaysDateFromCurrentWithPlanData 从当前未完成的天开始重新计算所有后续天的学习日期
// 最优化版本：接受已获取的plan对象和allDays数据，避免所有重复数据库查询
func (p *PlanModel) UpdatePlanDaysDateFromCurrentWithPlanData(plan *LearningPlan, allDays []*LearningPlanDay) error {
	return p.Tx(func(txDb *dbx.DBExtension) error {
		return p.updatePlanDaysDateFromCurrentCore(txDb, plan, allDays)
	})
}

// updatePlanDaysDateFromCurrentCore 核心的日期重新计算逻辑（不包装事务）
// 可以在已有事务中调用，也可以独立使用
func (p *PlanModel) updatePlanDaysDateFromCurrentCore(txDb *dbx.DBExtension, plan *LearningPlan, allDays []*LearningPlanDay) error {
	if len(allDays) == 0 {
		return nil // 没有学习天，无需调整
	}

	// 找到第一个未开始的学习天
	// 只有未开始的天(status=0)才能顺延到下一天
	// status=1(进行中)和status=2(已完成)都属于历史数据，不能改动
	var firstNotStartedIndex = -1
	for i, day := range allDays {
		if day.Status == int(enum.PlanDayStatusNotStarted) { // 只有未开始的天
			firstNotStartedIndex = i
			break
		}
	}

	if firstNotStartedIndex == -1 {
		return nil // 没有未开始的天，无需调整
	}

	// 从第一个未开始的天开始重新计算日期
	return p.recalculateDaysFromIndex(txDb, plan, allDays, firstNotStartedIndex)
}

// recalculateDaysFromIndex 从指定索引开始重新计算学习天的日期
func (p *PlanModel) recalculateDaysFromIndex(txDb *dbx.DBExtension, plan *LearningPlan, allDays []*LearningPlanDay, startIndex int) error {
	now := timex.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// 计算开始日期
	startDate := today

	// 如果startIndex > 0，说明前面有已完成的天，从最后一个已完成天的第二天开始
	if startIndex > 0 {
		lastCompletedDay := allDays[startIndex-1]
		lastCompletedDate := timex.TimestampToTime(lastCompletedDay.StudyTimestamp)
		startDate = lastCompletedDate.AddDate(0, 0, 1)

		// 确保不早于今天
		if startDate.Before(today) {
			startDate = today
		}
	}

	// 重新分配从startIndex开始的所有未开始学习天的日期
	// 只有未开始的天(status=0)才能顺延到下一天
	// status=1(进行中)和status=2(已完成)都属于历史数据，不能改动
	currentDate := startDate
	studyDaysOfWeek := plan.StudyDaysOfWeek

	for i := startIndex; i < len(allDays); i++ {
		day := allDays[i]

		// 只有未开始的天(status=0)才进行日期调整
		if day.Status == int(enum.PlanDayStatusNotStarted) {
			// 找到下一个符合StudyDaysOfWeek配置的日期
			nextStudyDate := p.findNextStudyDate(currentDate, studyDaysOfWeek)

			// 更新学习天的日期
			day.StudyTimestamp = timex.GetDateZeroTimestamp(nextStudyDate)
			if err := txDb.Save(day).Error; err != nil {
				return fmt.Errorf("failed to update day %s date: %w", day.Id, err)
			}

			// 下一次循环从第二天开始查找
			currentDate = nextStudyDate.AddDate(0, 0, 1)
		}
		// status=1(进行中)和status=2(已完成)的天不进行调整，保持历史数据
	}

	return nil
}

// findNextStudyDate 从指定日期开始找到下一个符合StudyDaysOfWeek配置的学习日期
func (p *PlanModel) findNextStudyDate(fromDate time.Time, studyDaysOfWeek types.IntArray) time.Time {
	if len(studyDaysOfWeek) == 0 {
		return fromDate // 如果没有配置，就返回当前日期
	}

	// 从fromDate开始逐天检查
	checkDate := fromDate
	for {
		// 获取当前日期是周几（1=周一，7=周日）
		weekday := int(checkDate.Weekday())
		if weekday == 0 {
			weekday = 7 // 将周日从0改为7
		}

		// 检查今天是否是学习日
		for _, day := range studyDaysOfWeek {
			if day == weekday {
				return checkDate
			}
		}

		// 检查下一天
		checkDate = checkDate.AddDate(0, 0, 1)
	}
}
