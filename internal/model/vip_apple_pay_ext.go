package model

import (
	"errors"
	"loop/pkg/dbx"
	"loop/pkg/timex"
	"loop/pkg/util"

	"github.com/go-pay/gopay/apple"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

func (v *VipModel) createAppleTradeOrder(uid string, tradeProduct TradeProduct, transactionInfo *apple.TransactionInfo) UserPurchaseOrder {
	newUUID := uuid.NewString()
	saver := UserPurchaseOrder{
		OrderNo:                    newUUID,
		Uid:                        uid,
		ProductID:                  tradeProduct.Id,
		ProductName:                tradeProduct.Name,
		ProductType:                tradeProduct.Type,
		Currency:                   tradeProduct.Currency,
		Amount:                     tradeProduct.Price,
		PaymentProvider:            1,
		OutTransactionId:           transactionInfo.TransactionId,
		AppleOriginalTransactionId: transactionInfo.OriginalTransactionId,
	}
	return saver
}

// 重新订阅、续订都需要更改订阅信息
func (v *VipModel) modifyWhenSubscriptionSign(txDb *dbx.DBExtension, uid string, tradeProduct TradeProduct, renewalInfo *apple.RenewalInfo, transactionInfo *apple.TransactionInfo) (UserSubscription, error) {
	userSubscription := UserSubscription{}
	if found, err := txDb.GetOne(&userSubscription, UserSubscription{Uid: uid, AppleOriginalTransactionId: transactionInfo.OriginalTransactionId, PaymentProvider: 1}); !found {
		if err != nil {
			return userSubscription, err
		}
		logrus.Error("ApplePayReviewHandler:not found UserSubscription uid=", uid, " OriginalTransactionId=", transactionInfo.OriginalTransactionId)
		return userSubscription, errors.New("ApplePayReviewHandler: not found UserSubscription")
	}
	userSubscription.FirstCycleAmount = float64(transactionInfo.Price) / 1000
	userSubscription.NextCycleAmount = tradeProduct.Price
	userSubscription.SignTimestamp = transactionInfo.PurchaseDate
	userSubscription.Uid = uid
	userSubscription.NextPaidDate = util.TsFormat2String(renewalInfo.RenewalDate)
	userSubscription.NextPaidTimestamp = renewalInfo.RenewalDate
	userSubscription.Status = UserSubscriptionStatusContract
	userSubscription.ProductID = tradeProduct.Id
	userSubscription.ProductName = tradeProduct.Name
	userSubscription.PaymentProvider = 1
	userSubscription.OutTransactionId = transactionInfo.TransactionId
	userSubscription.AppleOriginalTransactionId = transactionInfo.OriginalTransactionId
	if err := txDb.Update(userSubscription, " id = ?", userSubscription.Id); err != nil {
		return userSubscription, err
	}
	return userSubscription, nil
}
func (v *VipModel) modVipWhenSubscribeSuccess(txDb *dbx.DBExtension, userSubscription UserSubscription, vipData VIP) error {
	days, err := v.sumDaysByUserVipFlow(txDb, userSubscription.Uid)
	if err != nil {
		return err
	}
	var userVIPRelations UserVIPRelations
	if found, err := txDb.GetOne(&userVIPRelations, UserVIPRelations{Uid: userSubscription.Uid}); !found {
		if err != nil {
			return err
		}
		logrus.Error("ApplePayUpgradeHandler:not found userVIPRelations uid=", userVIPRelations.Uid, "userSubscription id=", userSubscription.Id)
		return errors.New("ApplePayUpgradeHandler: not found userVIPRelations")
	}
	expireTimestamp := timex.Now().UnixMilli() + int64(days)*24*60*60*1000
	userVIPRelations.ExpireDate = util.TsFormat2String(expireTimestamp)
	userVIPRelations.IsSubscription = 1
	userVIPRelations.VipID = vipData.Id
	userVIPRelations.IsVip = 1
	userVIPRelations.ExpireTimestamp = expireTimestamp
	if err := txDb.Update(userVIPRelations, " id = ?", userVIPRelations.Id); err != nil {
		return err
	}
	return nil
}

func (v *VipModel) logUserSubscription(txDb *dbx.DBExtension, userSubscription UserSubscription, operation int) error {
	userSubscriptionLog := UserSubscriptionLog{}
	userSubscriptionLog.ProductID = userSubscription.ProductID
	userSubscriptionLog.ProductName = userSubscription.ProductName
	userSubscriptionLog.PaymentProvider = userSubscription.PaymentProvider
	userSubscriptionLog.Operation = operation
	userSubscriptionLog.Uid = userSubscription.Uid
	userSubscriptionLog.UserSubscriptionID = userSubscription.Id
	err := txDb.SaveOne(&userSubscriptionLog)
	if err != nil {
		return err
	}
	return nil
}

// 统计用户会员总天数
func (v *VipModel) sumDaysByUserVipFlow(txDb *dbx.DBExtension, uid string) (int64, error) {
	var totalDuration int64

	// 查询指定 UID 的 DurationSeconds 总和 不论是来自哪个平台
	err := txDb.Model(&UserVIPFlow{}).
		Where("uid = ?", uid).
		Select("SUM(days)").
		Scan(&totalDuration).Error
	if err != nil {
		return 0, err
	}
	return totalDuration, nil
}
