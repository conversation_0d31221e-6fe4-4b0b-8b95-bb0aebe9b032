package model

import (
	"loop/internal/config"

	"go4.org/syncutil/singleflight"
)

// UserFeedback 用户反馈表
type UserFeedback struct {
	Model
	Uid         string `gorm:"not null;index:idx_user_feedback,priority:1;comment:用户ID"`
	Content     string `gorm:"type:text;not null;comment:反馈内容"`
	LogOssUrl   string `gorm:"size:500;comment:日志OSS地址"`
	ContactInfo string `gorm:"size:255;comment:联系方式"`
	Status      int    `gorm:"size:20;default:0;comment:状态 0待处理 1已处理 2已关闭"`
	DeviceInfo  string `gorm:"size:500;comment:设备信息"`
	AppVersion  string `gorm:"size:100;comment:应用版本"`
}

func (UserFeedback) TableName() string {
	return "user_feedbacks"
}

func NewFeedbackModel(dbModel *DbModel, config *config.Config) *FeedbackModel {
	return &FeedbackModel{
		DbModel: dbModel,
		config:  config,
		sg:      &singleflight.Group{},
	}
}

type FeedbackModel struct {
	*DbModel
	config *config.Config
	sg     *singleflight.Group
}

func (f *FeedbackModel) GetById(id string) (*UserFeedback, error) {
	result := UserFeedback{}
	if found, err := f.GetOne(&result, "id = ?", id); !found {
		return nil, err
	}
	return &result, nil
}

func (f *FeedbackModel) GetByUid(uid string) ([]UserFeedback, error) {
	var results []UserFeedback
	if err := f.GetOrderedList(&results, "created_at DESC", "uid = ?", uid); err != nil {
		return nil, err
	}
	return results, nil
}

func (f *FeedbackModel) CreateFeedback(feedback *UserFeedback) error {
	return f.SaveOne(feedback)
}

func (f *FeedbackModel) UpdateStatus(id string, status int, adminReply string) error {
	updates := map[string]interface{}{
		"status": status,
	}
	if adminReply != "" {
		updates["admin_reply"] = adminReply
	}
	return f.DB.Model(&UserFeedback{}).Where("id = ?", id).Updates(updates).Error
}

func (f *FeedbackModel) GetFeedbackList(page, pageSize int, status *int) ([]UserFeedback, int64, error) {
	var feedbacks []UserFeedback
	var total int64

	query := f.DB
	if status != nil {
		query = query.Where("status = ?", *status)
	}

	// 获取总数
	if err := query.Model(&UserFeedback{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&feedbacks).Error; err != nil {
		return nil, 0, err
	}

	return feedbacks, total, nil
}
