package model

import (
	"loop/internal/config"
	"loop/internal/response"
	"loop/pkg/timex"
	"time"

	"errors"
	"loop/pkg/util"

	"github.com/sirupsen/logrus"
	"go4.org/syncutil/singleflight"
)

// 用户每个剧集的数据
type DataEpisode struct {
	ModelAutoId
	Uid                     string `gorm:"not null;index:idx_user_watch,priority:1" json:"uid"`
	ResourceId              string `gorm:"type:varchar(20)"`       // 视频资源ID
	ResourceType            int    `gorm:"type:tinyint;default:0"` //视频类型 1代表远程的资源 2代表本地资源
	Status                  int    `gorm:"size:2;not null"`        ////0进行中、1已完成
	TargetLsTimes           int64  `gorm:"type:bigint;default:0"`  //目标LS次数，默认100
	CurrentLsTimes          int64  `gorm:"type:bigint;default:0"`
	LearnDurationWhenFinish int64  `gorm:"type:bigint;default:0"` //完成LS的时候完成的分钟数 需要固定
	StartTime               int64  `gorm:"type:bigint;default:0"` //剧集LS开始学习时间
	EndTime                 int64  `gorm:"type:bigint;default:0"` //剧集LS完成学习时间,可以为空
}

func (DataEpisode) TableName() string {
	return "data_episodes"
}

// 用户每次的学习数据
type DataEpisodeEach struct {
	ModelAutoId
	Uid                  string `gorm:"not null;index:idx_user_watch,priority:1" json:"uid"`
	ResourceId           string `gorm:"type:varchar(20)"`                // 视频资源ID
	ResourceType         int    `gorm:"type:tinyint;default:0"`          //视频类型 1代表远程的资源 2代表本地资源
	CurrentLsTimes       int64  `gorm:"type:bigint;default:0"`           //当前是第几遍LS，用来计算每个LS遍数的统计
	SessionStartTime     int64  `gorm:"type:bigint;default:0"`           //每次记录的开始时间 毫秒
	SessionEndTime       int64  `gorm:"type:bigint;default:0"`           // 每次记录的结束时间 毫秒
	TotalSessionDuration int64  `gorm:"type:bigint;default:0"`           // 播放总时长，毫秒
	TotalPlayDuration    int64  `gorm:"type:bigint;default:0"`           // 播放总时长，毫秒
	TotalRecordDuration  int64  `gorm:"type:bigint;default:0"`           // 录音总时长，毫秒
	Sentences            string `gorm:"type:LONGTEXT;" json:"sentences"` //dto.SentenceLearning
}

func (DataEpisodeEach) TableName() string {
	return "data_episode_eachs"
}
func NewDataCenterModel(dbModel *DbModel, config *config.Config) *DataCenterModel {
	return &DataCenterModel{
		DbModel: dbModel,
		config:  config,
		sg:      &singleflight.Group{}}
}

type DataCenterModel struct {
	*DbModel
	config *config.Config
	sg     *singleflight.Group
}

func (r *DataCenterModel) GetDataEpisodeAndCreate(uid string, resourceId string, resourceType int) (*DataEpisode, error) {
	query := DataEpisode{Uid: uid, ResourceId: resourceId, ResourceType: resourceType}
	result := DataEpisode{}
	if found, err := r.GetOne(&result, query); !found {
		if err != nil {
			return nil, err
		}
		logrus.Info("User ", uid, "  create newDataEpisode")
		now := timex.Now().UnixMilli() // 直接获取毫秒时间戳
		newDataEpisode := DataEpisode{
			Uid:            uid,
			ResourceId:     resourceId,
			ResourceType:   resourceType,
			Status:         0,
			TargetLsTimes:  100,
			CurrentLsTimes: 1,
			StartTime:      now,
		}
		if err := r.SaveOne(&newDataEpisode); err != nil {
			return nil, err
		}
		return &result, nil
	}
	return &result, nil
}

func (r *DataCenterModel) GetDataEpisode(uid string, resourceId string, resourceType int) (*DataEpisode, error) {
	query := DataEpisode{Uid: uid, ResourceId: resourceId, ResourceType: resourceType}
	var result DataEpisode
	err := r.GetList(&result, query)
	if err != nil {
		return nil, err
	}
	return &result, nil
}
func (r *DataCenterModel) GetDataEpisodeList(uid string) ([]DataEpisode, error) {
	query := DataEpisode{Uid: uid}
	var result []DataEpisode
	err := r.GetOrderedList(&result, "updated_at DESC", query)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (r *DataCenterModel) GetTodayDataEpisodeDailys(uid string, resourceId string, resourceType int) ([]*DataEpisodeEach, error) {
	now := timex.Now()
	location := now.Location()
	startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, location)
	endOfDay := startOfDay.Add(24 * time.Hour)
	startTimestamp := startOfDay.UnixMilli() // 直接获取毫秒时间戳
	endTimestamp := endOfDay.UnixMilli()     // 直接获取毫秒时间戳
	result := []*DataEpisodeEach{}
	err := r.GetList(&result, "uid = ? AND resource_id = ? AND resource_type = ? AND session_start_time >= ? AND session_end_time < ?", uid, resourceId, resourceType, startTimestamp, endTimestamp)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (r *DataCenterModel) GetWeekDataEpisode(uid string, start, end time.Time) (map[int][]*DataEpisodeEach, error) {

	// Prepare a map to store data by day
	weekData := make(map[int][]*DataEpisodeEach)

	episodes, err := r.GetDataEpisodeByDate(uid, start, end)
	if err != nil {
		return nil, err
	}

	// Organize the data by day
	for _, episode := range episodes {
		weekDay := time.UnixMilli(episode.SessionStartTime).Weekday()
		day := (int(weekDay) + 6) % 7 // Map Monday=0, ..., Sunday=6
		weekData[day] = append(weekData[day], episode)
	}

	return weekData, nil
}

func (r *DataCenterModel) GetDayDataEpisode(uid string, start, end time.Time) (map[int][]*DataEpisodeEach, error) {

	dayData := make(map[int][]*DataEpisodeEach)
	episodes, err := r.GetDataEpisodeByDate(uid, start, end)
	if err != nil {
		return nil, err
	}

	for _, episode := range episodes {
		hour := time.UnixMilli(episode.SessionStartTime).Hour()
		dayData[hour] = append(dayData[hour], episode)
	}

	return dayData, nil
}
func (r *DataCenterModel) GetMonthDataEpisode(uid string, start, end time.Time) (map[int][]*DataEpisodeEach, error) {

	monthData := make(map[int][]*DataEpisodeEach)
	episodes, err := r.GetDataEpisodeByDate(uid, start, end)
	if err != nil {
		return nil, err
	}

	for _, episode := range episodes {
		day := time.UnixMilli(episode.SessionStartTime).Day()
		monthData[day] = append(monthData[day], episode)
	}

	return monthData, nil
}
func (r *DataCenterModel) GetYearDataEpisode(uid string, start, end time.Time) (map[int][]*DataEpisodeEach, error) {
	yearData := make(map[int][]*DataEpisodeEach)
	episodes, err := r.GetDataEpisodeByDate(uid, start, end)
	if err != nil {
		return nil, err
	}

	for _, episode := range episodes {
		month := time.UnixMilli(episode.SessionStartTime).Month()
		yearData[int(month)] = append(yearData[int(month)], episode)
	}

	return yearData, nil
}
func (r *DataCenterModel) GetDataEpisodeByDate(uid string, start, end time.Time) ([]*DataEpisodeEach, error) {
	// 使用结构体查询替代字符串查询
	query := DataEpisodeEach{
		Uid: uid,
	}

	var episodes []*DataEpisodeEach
	err := r.Where(&query).Where("session_start_time >= ? AND session_start_time < ?", start.UnixMilli(), end.UnixMilli()).Find(&episodes).Error
	if err != nil {
		return nil, err
	}
	return episodes, nil
}

type TotalDataEpisodeResult struct {
	TotalLearnDuration int64
	DayCount           int64
}

func (r *DataCenterModel) GetTotalDataEpisode(uid string, resourceId string, resourceType int) (*TotalDataEpisodeResult, error) {
	return r.GetTotalDataEpisodeByTime(uid, resourceId, resourceType, nil, nil)
}

func (r *DataCenterModel) GetTotalDataEpisodeByTime(uid string, resourceId string, resourceType int, start, end *time.Time) (*TotalDataEpisodeResult, error) {
	// 参数验证
	if !util.IsValidID(uid) {
		return nil, errors.New("invalid uid format")
	}
	if resourceId != "" && !util.IsValidID(resourceId) {
		return nil, errors.New("invalid resource id format")
	}

	var result TotalDataEpisodeResult

	// 使用更安全的日期处理方式，将毫秒时间戳转换为秒
	query := r.Model(&DataEpisodeEach{}).
		Select("SUM(total_session_duration) AS total_learn_duration, COUNT(DISTINCT DATE(FROM_UNIXTIME(session_start_time/1000))) AS day_count").
		Where("uid = ? AND DATE(FROM_UNIXTIME(session_start_time/1000)) = DATE(FROM_UNIXTIME(session_end_time/1000))", uid)

	if start != nil && end != nil {
		query = query.Where("session_start_time >= ? AND session_start_time < ?", start.UnixMilli(), end.UnixMilli())
	}

	if resourceId != "" {
		query = query.Where("resource_id = ?", resourceId)
	}

	if resourceType != 0 {
		query = query.Where("resource_type = ?", resourceType)
	}

	// 添加日志
	logrus.WithFields(logrus.Fields{
		"operation":  "GetTotalDataEpisodeByTime",
		"uid":        uid,
		"resourceId": resourceId,
		"timestamp":  time.Now(),
	}).Info("Data Query Log")

	err := query.Scan(&result).Error
	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (r *DataCenterModel) GetEpisodeLsData(uid string, resourceId string, resourceType int) ([]response.EpisodeLsData, error) {
	var results []response.EpisodeLsData

	// 查询每个 CurrentLsTimes 的 LearnDuration 总和，并获取最新的结束时间。
	query := r.Model(&DataEpisodeEach{}).
		Select("current_ls_times AS ls_times, SUM(total_session_duration) AS total_learn_duration, MAX(session_end_time) AS finish_time").
		Where("uid = ?", uid).
		Group("current_ls_times").
		Order("ls_times DESC") // 添加排序

	if resourceId != "" {
		query = query.Where("resource_id = ?", resourceId)
	}

	if resourceType != 0 {
		query = query.Where("resource_type = ?", resourceType)
	}

	err := query.Scan(&results).Error

	if err != nil {
		return nil, err
	}
	return results, nil
}
