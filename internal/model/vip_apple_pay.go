package model

import (
	"errors"
	"fmt"
	"loop/pkg/dbx"
	"loop/pkg/timex"
	"loop/pkg/util"

	"github.com/go-pay/gopay/apple"
	"github.com/sirupsen/logrus"
)

type AppleNotificationHandler interface {
	Handle(r *VipModel, tradeProduct TradeProduct, vipData VIP, tradeOrder UserPurchaseOrder, notificationV2Payload *apple.NotificationV2Payload, renewalInfo *apple.RenewalInfo, transactionInfo *apple.TransactionInfo) error
}

func (v *VipModel) selectHandler(notificationV2Payload *apple.NotificationV2Payload) AppleNotificationHandler {
	if notificationV2Payload.NotificationType == apple.NotificationTypeV2Subscribed {
		if notificationV2Payload.Subtype == apple.SubTypeV2InitialBuy {
			//代表第一次购买
			return &ApplePayInitBuyHandler{}
		} else if notificationV2Payload.Subtype == apple.SubTypeV2Resubscribe {
			// 过期订阅的开始重新订阅，将状态更改为订阅中 用于SUBSCRIBED. 带有此信息的通知表明用户通过家庭共享重新订阅或接收了对同一订阅或同一订阅组内的另一个订阅的访问权限。
			//和续订的逻辑是一样的
			return &ApplePayReviewHandler{}
		}
	} else if notificationV2Payload.NotificationType == apple.NotificationTypeV2DidRenew {
		// subtype是BILLING_RECOVERY，则之前续订失败的过期订阅已成功续订。如果子状态为空（续订），则活动订阅已成功自动续订新的交易周期。为客户提供对订阅内容或服务的访问权限。
		//自动续订
		return &ApplePayReviewHandler{}
	} else if notificationV2Payload.NotificationType == apple.NotificationTypeV2DidChangeRenewalPref {
		if notificationV2Payload.Subtype == apple.SubTypeV2Upgrade {
			//升级订阅
			return &ApplePayUpgradeHandler{}
		} else if notificationV2Payload.Subtype == apple.SubTypeV2Downgrade {
			//包含此信息的通知表明用户降级了其订阅或交叉分级为具有不同持续时间的订阅。降级将在下一个续订日期生效。 不需要管，因为会自动续费，只做记录
			return &ApplePayDowngradeHandler{}
		}
	} else if notificationV2Payload.NotificationType == apple.NotificationTypeV2DidChangeRenewalStatus {
		// 一种通知类型，与其一起subtype指示用户对订阅续订状态进行了更改。如果subtype=AUTO_RENEW_ENABLED ，则用户重新启用订阅自动续订。
		// 如果是AUTO_RENEW_DISABLED，则用户禁用了订阅自动续费，或者用户申请退款后App Store禁用了订阅自动续费。
		if notificationV2Payload.Subtype == apple.SubTypeV2AutoRenewDisabled {
			// 表明用户禁用了订阅自动续订，或者 App Store 在用户申请退款后禁用了订阅自动续订。 只做记录
			return &ApplePayCancelHandler{}
		} else if notificationV2Payload.Subtype == apple.SubTypeV2AutoRenewEnabled {
			// 表明用户启用了订阅自动续订 只做记录
			return &ApplePayCancelAndReSubscribeHandler{}
		}
	}
	return &ApplePayEmptyHandler{}
}

type ApplePayEmptyHandler struct{}

func (h *ApplePayEmptyHandler) Handle(r *VipModel, tradeProduct TradeProduct, vipData VIP, tradeOrder UserPurchaseOrder, notificationV2Payload *apple.NotificationV2Payload, renewalInfo *apple.RenewalInfo, transactionInfo *apple.TransactionInfo) error {
	return nil
}

type ApplePayInitBuyHandler struct{}

func (h *ApplePayInitBuyHandler) Handle(r *VipModel, tradeProduct TradeProduct, vipData VIP, tradeOrder UserPurchaseOrder, notificationV2Payload *apple.NotificationV2Payload, renewalInfo *apple.RenewalInfo, transactionInfo *apple.TransactionInfo) error {
	uid := tradeOrder.Uid
	userSubscription := UserSubscription{}
	err := r.Tx(
		func(txDb *dbx.DBExtension) error {
			//添加订阅信息
			userSubscription = UserSubscription{
				Uid:                        uid,
				ProductID:                  tradeOrder.ProductID,
				ProductName:                tradeOrder.ProductName,
				PaymentProvider:            1,
				FirstCycleAmount:           float64(transactionInfo.Price) / 1000,
				NextCycleAmount:            tradeProduct.Price,
				OutTransactionId:           transactionInfo.TransactionId,
				AppleOriginalTransactionId: transactionInfo.OriginalTransactionId,
				SignTimestamp:              transactionInfo.PurchaseDate,
				NextPaidDate:               util.TsFormat2String(transactionInfo.ExpiresDate),
				NextPaidTimestamp:          transactionInfo.ExpiresDate,
				Currency:                   transactionInfo.Currency,
			}
			err := txDb.SaveOne(&userSubscription)
			if err != nil {
				return err
			}
			//记录订阅日志
			err = r.logUserSubscription(txDb, userSubscription, UserSubscriptionOperationContract)
			if err != nil {
				return err
			}
			return nil
		},
		func(txDb *dbx.DBExtension) error {
			//更改原来的订单信息
			tradeOrder.Status = TradeOrderStatusPayFinish
			tradeOrder.OutTransactionId = transactionInfo.TransactionId
			tradeOrder.AppleOriginalTransactionId = transactionInfo.OriginalTransactionId
			tradeOrder.PaidTimestamp = transactionInfo.PurchaseDate
			tradeOrder.Currency = transactionInfo.Currency
			tradeOrder.UserSubscriptionID = userSubscription.Id
			if err := txDb.Update(tradeOrder, " id = ?", tradeOrder.Id); err != nil {
				return err
			}
			return nil
		},
		func(txDb *dbx.DBExtension) error {
			// 新增用户会员流水 增加对应的时间
			userVIPFlow := UserVIPFlow{
				Uid:                uid,
				Title:              "User First Buy:" + tradeProduct.Name,
				Operation:          2,
				OperationTimestamp: timex.Now().UnixMilli(),
				Days:               tradeProduct.Days,
				Terminal:           2,
				BizID:              transactionInfo.AppAccountToken,
			}
			err := txDb.SaveOne(&userVIPFlow)
			if err != nil {
				return err
			}
			return nil
		},
		func(txDb *dbx.DBExtension) error {
			//新增用户会员信息 可能存在直接送会员的情况已经创建了这个表  所以需要先查询
			var userVIPRelations UserVIPRelations
			found, err := txDb.GetOne(&userVIPRelations, UserVIPRelations{Uid: uid})
			if err != nil {
				return err
			}
			days, err := r.sumDaysByUserVipFlow(txDb, uid)
			if err != nil {
				return err
			}
			expireTimestamp := timex.Now().UnixMilli() + int64(days)*24*60*60*1000
			if !found {
				userVIPRelations = UserVIPRelations{
					Uid:             uid,
					IsVip:           1,
					VipID:           vipData.Id,
					IsSubscription:  1,
					ExpireDate:      util.TsFormat2String(expireTimestamp),
					ExpireTimestamp: expireTimestamp,
				}
				err := txDb.SaveOne(&userVIPRelations)
				if err != nil {
					return err
				}
			} else {
				userVIPRelations.Uid = uid
				userVIPRelations.ExpireDate = util.TsFormat2String(expireTimestamp)
				userVIPRelations.IsSubscription = 1
				userVIPRelations.VipID = vipData.Id
				userVIPRelations.IsVip = 1
				userVIPRelations.ExpireTimestamp = expireTimestamp
				if err := txDb.Update(userVIPRelations, " id = ?", userVIPRelations.Id); err != nil {
					return err
				}
			}

			return nil
		},
	)
	if err != nil {
		return err
	}
	return nil
}

type ApplePayReviewHandler struct{}

func (h *ApplePayReviewHandler) Handle(r *VipModel, tradeProduct TradeProduct, vipData VIP, tradeOrder UserPurchaseOrder, notificationV2Payload *apple.NotificationV2Payload, renewalInfo *apple.RenewalInfo, transactionInfo *apple.TransactionInfo) error {
	uid := tradeOrder.Uid
	userSubscription := UserSubscription{}
	var err error
	err = r.Tx(
		func(txDb *dbx.DBExtension) error {
			saver := r.createAppleTradeOrder(uid, tradeProduct, transactionInfo)
			saver.Status = TradeOrderStatusPayFinish
			err := txDb.SaveOne(&saver)
			if err != nil {
				return err
			}
			return nil
		},
		func(txDb *dbx.DBExtension) error {
			userSubscription, err = r.modifyWhenSubscriptionSign(txDb, uid, tradeProduct, renewalInfo, transactionInfo)
			if err != nil {
				return err
			}
			return nil
		},
		func(txDb *dbx.DBExtension) error {
			// 新增用户会员流水 增加对应的时间
			userVIPFlow := UserVIPFlow{
				Uid:                uid,
				Title:              "用户自动续订或者是订阅:" + tradeProduct.Name,
				Operation:          2,
				OperationTimestamp: timex.Now().UnixMilli(),
				Days:               tradeProduct.Days,
				Terminal:           2,
				BizID:              transactionInfo.AppAccountToken,
			}
			err := txDb.SaveOne(&userVIPFlow)
			if err != nil {
				return err
			}
			return nil
		},
		func(txDb *dbx.DBExtension) error {
			//修改用户会员信息
			err := r.modVipWhenSubscribeSuccess(txDb, userSubscription, vipData)
			if err != nil {
				return err
			}
			return nil
		},
		func(txDb *dbx.DBExtension) error {
			//记录订阅日志
			err := r.logUserSubscription(txDb, userSubscription, UserSubscriptionOperationRenewal)
			if err != nil {
				return err
			}
			return nil
		},
	)
	if err != nil {
		return err
	}
	return nil
}

type ApplePayUpgradeHandler struct{}

func (h *ApplePayUpgradeHandler) Handle(r *VipModel, tradeProduct TradeProduct, vipData VIP, tradeOrder UserPurchaseOrder, notificationV2Payload *apple.NotificationV2Payload, renewalInfo *apple.RenewalInfo, transactionInfo *apple.TransactionInfo) error {
	//TODO 记录本次因为升级所退的金额
	uid := tradeOrder.Uid
	userSubscription := UserSubscription{}
	var err error
	err = r.Tx(
		func(txDb *dbx.DBExtension) error {
			saver := r.createAppleTradeOrder(uid, tradeProduct, transactionInfo)
			saver.Status = TradeOrderStatusPayFinish
			err := txDb.SaveOne(&saver)
			if err != nil {
				return err
			}
			return nil
		},
		func(txDb *dbx.DBExtension) error {
			userSubscription := UserSubscription{}
			if found, err := txDb.GetOne(&userSubscription, UserSubscription{Uid: uid, AppleOriginalTransactionId: transactionInfo.OriginalTransactionId, PaymentProvider: 1}); !found {
				if err != nil {
					return err
				}
				logrus.Error("ApplePayReviewHandler:not found UserSubscription uid=", uid, " OriginalTransactionId=", transactionInfo.OriginalTransactionId)
				return errors.New("ApplePayReviewHandler: not found UserSubscription")
			}
			// 新增用户会员流水 减去原来的低级订阅的时长 因为签约的时候 UserSubscription 存的是最新的签约信息，也就是比如月卡的签约时间戳
			// 计算当前时间的时间戳-月卡的签约时间戳 得出的就是月卡已经消耗了多少
			days := util.CalculateDays(userSubscription.SignTimestamp, timex.Now().UnixMilli())
			userVIPFlow := UserVIPFlow{
				Uid:                uid,
				Title:              "用户升级了订阅:" + tradeProduct.Name + " 需要回收天数" + fmt.Sprint(days),
				Operation:          2,
				OperationTimestamp: timex.Now().UnixMilli(),
				Days:               -days,
				Terminal:           2,
				BizID:              transactionInfo.AppAccountToken,
			}
			err := txDb.SaveOne(&userVIPFlow)
			if err != nil {
				return err
			}
			return nil
		},
		func(txDb *dbx.DBExtension) error {
			// 新增用户会员流水 升级的时间
			userVIPFlow := UserVIPFlow{
				Uid:                uid,
				Title:              "用户升级订阅到:" + tradeProduct.Name,
				Operation:          2,
				OperationTimestamp: timex.Now().UnixMilli(),
				Days:               tradeProduct.Days,
				Terminal:           2,
				BizID:              transactionInfo.AppAccountToken,
			}
			err := txDb.SaveOne(&userVIPFlow)
			if err != nil {
				return err
			}
			return nil
		},
		func(txDb *dbx.DBExtension) error {
			//根据OriginalTransactionId 来获取订阅信息 更改订阅信息 更改订阅的时长
			//把原来的订阅信息的时间 直接修改为升级后的
			userSubscription, err = r.modifyWhenSubscriptionSign(txDb, uid, tradeProduct, renewalInfo, transactionInfo)
			if err != nil {
				return err
			}
			return nil
		},

		func(txDb *dbx.DBExtension) error {
			//记录订阅日志
			err := r.logUserSubscription(txDb, userSubscription, UserSubscriptionOperationUpgrade)
			if err != nil {
				return err
			}
			return nil
		},
		func(txDb *dbx.DBExtension) error {
			//修改用户会员信息
			err := r.modVipWhenSubscribeSuccess(txDb, userSubscription, vipData)
			if err != nil {
				return err
			}
			return nil
		},
	)
	if err != nil {
		return err
	}
	return nil
}

type ApplePayDowngradeHandler struct{}

func (h *ApplePayDowngradeHandler) Handle(r *VipModel, tradeProduct TradeProduct, vipData VIP, tradeOrder UserPurchaseOrder, notificationV2Payload *apple.NotificationV2Payload, renewalInfo *apple.RenewalInfo, transactionInfo *apple.TransactionInfo) error {
	uid := tradeOrder.Uid
	userSubscription := UserSubscription{}
	err := r.Tx(
		func(txDb *dbx.DBExtension) error {
			if found, err := r.GetOne(&userSubscription, UserSubscription{Uid: uid, AppleOriginalTransactionId: transactionInfo.OriginalTransactionId, PaymentProvider: 1}); !found {
				if err != nil {
					return err
				}
				logrus.Error("ApplePayDowngradeHandler:not found UserSubscription uid=", tradeOrder.Uid)
				return errors.New("ApplePayDowngradeHandler:not found UserSubscription")
			}
			return nil
		},
		func(txDb *dbx.DBExtension) error {
			//记录订阅日志
			err := r.logUserSubscription(txDb, userSubscription, UserSubscriptionOperationDowngrade)
			if err != nil {
				return err
			}
			return nil
		},
	)
	if err != nil {
		return err
	}
	return nil
}

type ApplePayCancelHandler struct{}

func (h *ApplePayCancelHandler) Handle(r *VipModel, tradeProduct TradeProduct, vipData VIP, tradeOrder UserPurchaseOrder, notificationV2Payload *apple.NotificationV2Payload, renewalInfo *apple.RenewalInfo, transactionInfo *apple.TransactionInfo) error {
	uid := tradeOrder.Uid
	userSubscription := UserSubscription{}
	err := r.Tx(
		func(txDb *dbx.DBExtension) error {
			if found, err := r.GetOne(&userSubscription, UserSubscription{Uid: uid, AppleOriginalTransactionId: transactionInfo.OriginalTransactionId, PaymentProvider: 1}); !found {
				if err != nil {
					return err
				}
				logrus.Error("cancelSubscribe:not found UserSubscription uid=", tradeOrder.Uid)
				return errors.New("cancelSubscribe:not found UserSubscription")
			}
			return nil
		},
		func(txDb *dbx.DBExtension) error {
			//记录订阅日志
			err := r.logUserSubscription(txDb, userSubscription, UserSubscriptionOperationTermination)
			if err != nil {
				return err
			}
			return nil
		},
	)
	if err != nil {
		return err
	}
	return nil
}

type ApplePayCancelAndReSubscribeHandler struct{}

func (h *ApplePayCancelAndReSubscribeHandler) Handle(r *VipModel, tradeProduct TradeProduct, vipData VIP, tradeOrder UserPurchaseOrder, notificationV2Payload *apple.NotificationV2Payload, renewalInfo *apple.RenewalInfo, transactionInfo *apple.TransactionInfo) error {
	uid := tradeOrder.Uid
	userSubscription := UserSubscription{}
	err := r.Tx(
		func(txDb *dbx.DBExtension) error {
			if found, err := r.GetOne(&userSubscription, UserSubscription{Uid: uid, AppleOriginalTransactionId: transactionInfo.OriginalTransactionId, PaymentProvider: 1}); !found {
				if err != nil {
					return err
				}
				logrus.Error("ApplePayCancelAndReSubscribeHandler:not found UserSubscription uid=", tradeOrder.Uid)
				return errors.New("ApplePayCancelAndReSubscribeHandler:not found UserSubscription")
			}
			return nil
		},
		func(txDb *dbx.DBExtension) error {
			//记录订阅日志
			err := r.logUserSubscription(txDb, userSubscription, UserSubscriptionOperationReview)
			if err != nil {
				return err
			}
			return nil
		},
	)
	if err != nil {
		return err
	}
	return nil
}
