package types

// BaseStageResponse 基础阶段响应结构体
type BaseStageResponse struct {
	StageDesc string `json:"stageDesc"`
	Objective string `json:"objective"`
}

// StageResourcesAIResponse 表示AI生成的阶段资源分配响应
type StageResourcesAIResponse struct {
	Stages []PlanStageAIResponse `json:"stages"`
}

// PlanStageAIResponse 表示AI生成的单个阶段计划
type PlanStageAIResponse struct {
	BaseStageResponse
	StartDate string               `json:"startDate"` // 格式化的时间字符串
	EndDate   string               `json:"endDate"`   // 格式化的时间字符串
	Weeks     []PlanWeekAIResponse `json:"weeks"`
}

// PlanWeekAIResponse 表示AI生成的周计划
type PlanWeekAIResponse struct {
	WeekNumber int                      `json:"weekNumber"`
	Resources  []PlanResourceAIResponse `json:"resources"`
}

// PlanResourceAIResponse 表示AI生成的资源分配
type PlanResourceAIResponse struct {
	ResourceId string `json:"resourceId"`
	LsCount    int    `json:"lsCount"`
}

// PlanAIResponse 表示AI生成的完整学习计划
type PlanAIResponse struct {
	Stages []PlanStageAIResponse `json:"stages"`
}

// StageResourceListResponse 表示AI生成的阶段资源列表响应
type StageResourceListResponse struct {
	Stages []StageResourceList `json:"stages"`
}

// StageResourceList 表示单个阶段的资源列表
type StageResourceList struct {
	BaseStageResponse
	ResourceIds []string `json:"resourceIds"` // 按顺序排列的资源ID列表
}

// StageResourcesSortResponse 表示AI生成的阶段资源排序响应
// 注意：这是AI服务的内部格式，用于接收AI返回的数据，然后转换为StageResourceListResponse格式对外提供
// 不建议在新代码中直接使用，应使用StageResourceListResponse
type StageResourcesSortResponse struct {
	Stages []StageSortResponse `json:"stages"`
}

// StageSortResponse 表示单个阶段的排序响应（内部格式）
type StageSortResponse struct {
	BaseStageResponse
	ResourceNumbers []int `json:"resourceNumbers"` // AI返回的资源序号，需要转换为实际的资源ID
}

// ResourceUsage 表示资源使用状态
type ResourceUsage struct {
	ResourceId string `json:"resourceId"`
	TotalCount int    `json:"totalCount"` // 总分配次数
	UsedCount  int    `json:"usedCount"`  // 已使用次数
	Duration   int    `json:"duration"`   // 资源时长（秒）
}
