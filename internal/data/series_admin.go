package data

import (
	"loop/internal/config"
	"loop/internal/model"
	"loop/internal/request"
	"loop/internal/response"
	"loop/pkg/enum"
	"loop/pkg/web"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/thoas/go-funk"
)

func NewSeriesAdminRepo(
	model *model.SeriesAdminModel,
	resourceModel *model.ResourceAdminModel,
	seriesModel *model.SeriesModel,
	config *config.Config,
) *SeriesAdminRepo {
	return &SeriesAdminRepo{
		model:         model,
		resourceModel: resourceModel,
		seriesModel:   seriesModel,
		config:        config,
	}
}

type SeriesAdminRepo struct {
	model         *model.SeriesAdminModel
	resourceModel *model.ResourceAdminModel
	seriesModel   *model.SeriesModel
	config        *config.Config
}

func (s *SeriesAdminRepo) SaveOne(c *gin.Context, req request.SeriesAddReq) *web.JsonResult {
	err := s.resourceModel.CheckCategoryIds(req.CategoryIds)
	if err != nil {
		return web.JsonInternalError(err)
	}
	err = s.model.AddSeries(req)
	if err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonOK()
}

func (s *SeriesAdminRepo) GetList(c *gin.Context, req request.ListReq) *web.JsonResult {
	var series []*model.Series
	count, err := s.model.GetOrderPage(&series, enum.SortType(req.SortType).DescString(), req.CurrentPage, req.PageSize)
	if err != nil {
		return web.JsonInternalError(err)
	}
	featuredContentIds, err := s.seriesModel.GetFeatureContentIds()
	if err != nil {
		return web.JsonInternalError(err)
	}

	var resps []response.SeriesDetailAdminResp
	copier.Copy(&resps, series)
	// 1. 查询所有剧集ID
	var seriesIds []string
	for _, s := range series {
		seriesIds = append(seriesIds, s.Id)
	}
	// 2. 查询所有剧集的多语言信息
	var allSeriesRelations []*model.SeriesRelation
	if len(seriesIds) > 0 {
		err = s.model.GetList(&allSeriesRelations, "series_id IN ?", seriesIds)
		if err != nil {
			return web.JsonInternalError(err)
		}
	}
	// 3. 按剧集ID分组
	seriesRelMap := make(map[string][]response.SeriesRelationResp)
	for _, rel := range allSeriesRelations {
		var respItem response.SeriesRelationResp
		copier.Copy(&respItem, rel)
		seriesRelMap[rel.SeriesId] = append(seriesRelMap[rel.SeriesId], respItem)
	}
	// 4. 查询所有剧集的分类关系
	var allCategoryRelations []*model.CategorySeriesRelations
	if len(seriesIds) > 0 {
		err = s.model.GetList(&allCategoryRelations, "series_id IN ?", seriesIds)
		if err != nil {
			return web.JsonInternalError(err)
		}
	}
	// 按series_id分组
	categoryMap := make(map[string][]string)
	for _, rel := range allCategoryRelations {
		categoryMap[rel.SeriesId] = append(categoryMap[rel.SeriesId], rel.CategoryId)
	}
	for i := range resps {
		resps[i].IsFeaturedContent = funk.Contains(featuredContentIds, resps[i].Id)
		resps[i].SeriesRelations = seriesRelMap[resps[i].Id]
		resps[i].CategoryIds = categoryMap[resps[i].Id]
	}
	return web.JsonData(web.PageJsonResult{
		Data:  resps,
		Total: count,
	})
}

func (s *SeriesAdminRepo) DeleteSeries(c *gin.Context, req request.IdReq) *web.JsonResult {
	err := s.model.DeleteSeries([]string{req.Id})
	if err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonOK()
}

func (s *SeriesAdminRepo) DeleteMultiSeries(c *gin.Context, req request.IdsReq) *web.JsonResult {
	err := s.model.DeleteSeries(req.Id)
	if err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonOK()
}

func (s *SeriesAdminRepo) GetSeriesDetail(c *gin.Context, req request.IdReq) *web.JsonResult {
	var result model.Series
	if found, err := s.model.GetOne(&result, "id = ?", req.Id); !found {
		if err != nil {
			return web.JsonInternalError(err)
		}
	}
	var resp response.SeriesDetailAdminResp
	var seriesCategories []model.CategorySeriesRelations
	err := s.model.Where(model.CategorySeriesRelations{SeriesId: req.Id}).Find(&seriesCategories).Error
	if err != nil {
		return web.JsonInternalError(err)
	}
	categoryIDs := make([]string, len(seriesCategories))
	for i, rc := range seriesCategories {
		categoryIDs[i] = rc.CategoryId
	}

	// 获取SeriesRelation数据
	var seriesRelations []model.SeriesRelation
	err = s.model.Where(model.SeriesRelation{SeriesId: req.Id}).Find(&seriesRelations).Error
	if err != nil {
		return web.JsonInternalError(err)
	}

	copier.Copy(&resp, result)
	featuredContentIds, err := s.seriesModel.GetFeatureContentIds()
	if err != nil {
		return web.JsonInternalError(err)
	}
	resp.IsFeaturedContent = funk.Contains(featuredContentIds, resp.Id)
	resp.CategoryIds = categoryIDs

	// 复制SeriesRelation数据到响应
	var seriesRelationResps []response.SeriesRelationResp
	copier.Copy(&seriesRelationResps, seriesRelations)
	resp.SeriesRelations = seriesRelationResps

	return web.JsonData(resp)
}

func (s *SeriesAdminRepo) SetFeaturedContent(c *gin.Context, req request.SeriesFeaturedContentReq) *web.JsonResult {
	found, err := s.model.GetOne(&model.FeaturedContent{}, model.FeaturedContent{ContentType: int(enum.Series), ContentID: req.SeriesId})
	if err != nil {
		return web.JsonInternalError(err)
	}
	if found {
		err := s.model.Delete(&model.FeaturedContent{}, model.FeaturedContent{ContentType: int(enum.Series), ContentID: req.SeriesId}).Error
		if err != nil {
			return web.JsonInternalError(err)
		}
	} else {
		err = s.model.SaveOne(&model.FeaturedContent{ContentType: int(enum.Series), ContentID: req.SeriesId, LangCode: req.LangCode})
		if err != nil {
			return web.JsonInternalError(err)
		}
	}
	return web.JsonOK()
}

func (s *SeriesAdminRepo) SetPriority(c *gin.Context, req request.PriorityReq) *web.JsonResult {
	var result model.Series
	found, err := s.model.GetOne(&result, model.Series{Model: model.Model{Id: req.Id}})
	if err != nil || !found {
		return web.JsonInternalError(err)
	}
	result.Priority = req.Priority
	if err := s.model.Update(&result, &model.Series{Model: model.Model{Id: req.Id}}); err != nil {
		return web.JsonInternalError(err)
	}

	return web.JsonOK()
}
