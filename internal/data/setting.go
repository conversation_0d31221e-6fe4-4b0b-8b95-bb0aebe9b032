package data

import (
	"loop/internal/config"
	"loop/internal/model"
	"loop/internal/request"
	"loop/internal/response"
	"loop/pkg/jwtx"
	"loop/pkg/web"

	"github.com/fatih/structs"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
)

func NewSettingRepo(
	model *model.UserModel,
	lsResourceModel *model.ResourceModel,
	config *config.Config,
) *SettingRepo {
	return &SettingRepo{
		model:           model,
		lsResourceModel: lsResourceModel,
		config:          config,
	}
}

type SettingRepo struct {
	model           *model.UserModel
	lsResourceModel *model.ResourceModel
	config          *config.Config
}

func (s *SettingRepo) FetchConfig(c *gin.Context) *web.JsonResult {
	uid := jwtx.GetUid(c)
	playerConfig, err := s.model.GetPlayerConfig(uid)
	if err != nil {
		return web.JsonInternalError(err)
	}
	playerConfigResp := response.UserPlayerConfigResp{}
	if playerConfig == nil {
		playerConfig = s.createDefautPlayerConfig(uid)
	}
	copier.Copy(&playerConfigResp, &playerConfig)
	userConfigResp := response.UserConfigResp{}
	userConfigResp.UserPlayerConfig = &playerConfigResp

	return web.JsonData(userConfigResp)
}

func (s *SettingRepo) UpdateUserPlayerConfig(c *gin.Context, req request.UpdateUserPlayerConfigRequest) *web.JsonResult {
	uid := jwtx.GetUid(c)
	playerConfig, err := s.model.GetPlayerConfig(uid)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if playerConfig == nil {
		playerConfig = s.createDefautPlayerConfig(uid)
	}
	if playerConfig == nil {
		return web.JsonInternalError(err)
	}

	fields := structs.Map(&req)
	for k, v := range fields {
		if v == nil {
			delete(fields, k)
		}
	}

	if err := s.model.Update(fields, &model.UserPlayerConfig{Uid: uid}); err != nil {
		return web.JsonInternalError(err)
	}
	return s.FetchConfig(c)
}

func (s *SettingRepo) createDefautPlayerConfig(uid string) *model.UserPlayerConfig {
	playerConfig := model.UserPlayerConfig{
		SubtitleFontSize:              16,
		SubtitleTextBottomHeightRatio: 0.0,
		SingleRepeatCount:             1,
		ShowSubtitleNum:               1,
		ShowSubtitleWhenRecordEnd:     true,
		AutoPlayRecordWhenRecordEnd:   true,
		Uid:                           uid,
	}
	err := s.model.SaveOne(&playerConfig)
	if err != nil {
		return nil
	}
	return &playerConfig
}
