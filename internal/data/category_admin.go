package data

import (
	"loop/internal/config"
	"loop/internal/model"
	"loop/internal/request"
	"loop/internal/response"
	"loop/pkg/enum"
	"loop/pkg/web"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
)

func NewCategoryAdminRepo(
	model *model.CategoryAdminModel,
	resRepo *ResourceRepo,
	config *config.Config,
) *CategoryAdminRepo {
	return &CategoryAdminRepo{
		model:   model,
		resRepo: resRepo,
		config:  config,
	}
}

type CategoryAdminRepo struct {
	model   *model.CategoryAdminModel
	resRepo *ResourceRepo
	config  *config.Config
}

func (s *CategoryAdminRepo) GetCategoryList(c *gin.Context, req request.GetCategoryListReq) *web.JsonResult {
	var categories []*model.Category
	var count int64
	var err error
	if req.CategoryTypeId != "" {
		count, err = s.model.GetOrderQueryPage(&categories, enum.SortType(req.SortType).DescString(), req.CurrentPage, req.PageSize, "category_type_id = ?", req.CategoryTypeId)
	} else {
		count, err = s.model.GetOrderPage(&categories, enum.SortType(req.SortType).DescString(), req.CurrentPage, req.PageSize)
	}
	if err != nil {
		return web.JsonInternalError(err)
	}
	resps := []response.CategoryResp{}
	copier.Copy(&resps, &categories)
	return web.JsonData(web.PageJsonResult{
		Data:  resps,
		Total: count,
	})
}
func (s *CategoryAdminRepo) SetPriority(c *gin.Context, req request.PriorityReq) *web.JsonResult {
	var result model.Category
	found, err := s.model.GetOne(&result, model.Category{Model: model.Model{Id: req.Id}})
	if err != nil || !found {
		return web.JsonInternalError(err)
	}
	result.Priority = req.Priority
	if err := s.model.Update(&result, &model.Category{Model: model.Model{Id: req.Id}}); err != nil {
		return web.JsonInternalError(err)
	}

	return web.JsonOK()
}
