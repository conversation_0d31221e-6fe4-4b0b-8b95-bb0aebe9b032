package data

import (
	"loop/internal/config"
	"loop/internal/model"
	"loop/internal/request"
	"loop/internal/response"
	"loop/pkg/enum"
	"loop/pkg/i18n"
	"loop/pkg/jwtx"
	"loop/pkg/web"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
)

func NewNoteRepo(
	model *model.VideoModel,
	resourceModel *model.ResourceModel,
	config *config.Config,
) *NotesRepo {
	return &NotesRepo{
		model:         model,
		resourceModel: resourceModel,
		config:        config,
	}
}

type NotesRepo struct {
	model         *model.VideoModel
	resourceModel *model.ResourceModel
	config        *config.Config
}

func (s *NotesRepo) AddOrUpdateNote(c *gin.Context, req request.NotesAddReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	if req.ResourceType == int(enum.LocalResource) {
		ucs, err := s.model.GetUserLocalResourceById(req.ResourceId)
		if err != nil {
			return web.JsonInternalError(err)
		}
		if ucs == nil {
			return web.JsonParamErr(i18n.T(c, i18n.ErrEntityNotFound))
		}
	} else {
		r, err := s.resourceModel.GetByIdString(req.ResourceId)
		if err != nil {
			return web.JsonInternalError(err)
		}
		if r == nil {
			return web.JsonParamErr(i18n.T(c, i18n.ErrEntityNotFound))
		}
	}

	query := model.Note{Uid: uid, ResourceId: req.ResourceId, ResourceType: req.ResourceType, VideoStartTime: req.VideoStartTime, VideoEndTime: req.VideoEndTime}
	result := model.Note{}
	found, err := s.model.GetOne(&result, query)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if found {
		result.Content = req.Content
		if err := s.model.Update(result, "id = ?", result.Id); err != nil {
			return web.JsonInternalError(err)
		}
		// 返回更新后的笔记
		return web.JsonData(result)
	} else {
		query.Uid = uid
		query.Content = req.Content
		query.ResourceId = req.ResourceId
		query.ResourceType = req.ResourceType
		query.VideoStartTime = req.VideoStartTime
		query.VideoEndTime = req.VideoEndTime
		if err := s.model.Save(&query).Error; err != nil {
			return web.JsonInternalError(err)
		}
		// 返回新创建的笔记
		return web.JsonData(query)
	}
}

func (s *NotesRepo) DeleteNote(c *gin.Context, req request.IdReq) *web.JsonResult {
	return DeleteById[model.Note](s.model.DbModel, req.Id)
}
func (s *NotesRepo) GetNoteList(c *gin.Context, req request.NotesReq) *web.JsonResult {
	result := []*model.Note{}
	err := s.model.GetPageRangeList(&result, "", req.CurrentPage, req.PageSize, &model.Note{Uid: jwtx.GetUid(c), ResourceId: req.ResourceId, ResourceType: req.ResourceType})
	if err != nil {
		return web.JsonInternalError(err)
	}
	var noteResp []response.NotesResp
	copier.Copy(&noteResp, result)
	return web.JsonData(noteResp)
}
func (s *NotesRepo) GetNote(c *gin.Context, req request.IdReq) *web.JsonResult {
	result := model.Note{}
	f, err := s.model.GetOne(&result, &model.Note{Model: model.Model{Id: req.Id}})
	if err != nil {
		return web.JsonInternalError(err)
	}
	if !f {
		return web.JsonParamErr(i18n.T(c, i18n.ErrEntityNotFound))
	}
	return web.JsonData(result)
}
