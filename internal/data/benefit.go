package data

import (
	"loop/internal/constants"
	"loop/internal/model"
	"loop/internal/request"
	"loop/pkg/jwtx"
	"loop/pkg/web"

	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
)

type BenefitRepo struct {
	db           *gorm.DB
	userRepo     *UserRepo
	benefitModel *model.BenefitModel
}

func NewBenefitRepo(db *gorm.DB, userRepo *UserRepo, benefitModel *model.BenefitModel) *BenefitRepo {
	return &BenefitRepo{
		db:           db,
		userRepo:     userRepo,
		benefitModel: benefitModel,
	}
}

// GetUserBenefits 获取用户当前的权益
func (r *BenefitRepo) GetUserBenefits(c *gin.Context) *web.JsonResult {
	userId := jwtx.GetUid(c)
	benefits, err := r.benefitModel.GetUserBenefits(userId)
	if err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonData(benefits)
}

// GetAllBenefits 获取所有权益定义
func (r *BenefitRepo) GetAllBenefits(req request.BenefitPageReq) *web.JsonResult {
	benefits, total, err := r.benefitModel.GetAllBenefits(req.PageSize, req.CurrentPage, req.Name, req.BenefitGroupCode, req.Status)
	if err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonData(web.PageJsonResult{
		Data:  benefits,
		Total: total,
	})
}

// CreateBenefit 创建权益
func (r *BenefitRepo) CreateBenefit(req request.CreateBenefitReq) *web.JsonResult {
	benefit := &model.Benefit{
		Name:           req.Name,
		Code:           constants.BenefitCode(req.Code),
		Level:          req.Level,
		CycleType:      req.CycleType,
		CycleCount:     req.CycleCount,
		BenefitCount:   req.BenefitCount,
		Sort:           req.Sort,
		Status:         req.Status,
		BenefitGroupID: req.BenefitGroupID,
		Description:    req.Description,
	}

	if err := r.benefitModel.CreateBenefit(benefit); err != nil {
		return web.JsonInternalError(err)
	}

	return web.JsonData(benefit)
}

// GetBenefitGroups 获取权益组下拉列表
func (r *BenefitRepo) GetBenefitGroups() *web.JsonResult {
	var groups []*model.BenefitGroup
	if err := r.db.Find(&groups).Error; err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonData(groups)
}

// CreateBenefitGroup 创建权益组
func (r *BenefitRepo) CreateBenefitGroup(req request.CreateBenefitGroupReq) *web.JsonResult {
	group := &model.BenefitGroup{
		Name:   req.Name,
		Code:   req.Code,
		Status: 1,
	}

	if err := r.benefitModel.CreateBenefitGroup(group); err != nil {
		return web.JsonInternalError(err)
	}

	return web.JsonData(group)
}

// UpdateBenefitGroup 更新权益组
func (r *BenefitRepo) UpdateBenefitGroup(req request.UpdateBenefitGroupReq) *web.JsonResult {
	group := &model.BenefitGroup{
		ModelAutoId: model.ModelAutoId{
			Id: req.Id,
		},
		Name: req.Name,
		Code: req.Code,
	}

	if err := r.benefitModel.UpdateBenefitGroup(group); err != nil {
		return web.JsonInternalError(err)
	}

	return web.JsonData(group)
}

// GetAllBenefitGroups 获取所有权益组
func (r *BenefitRepo) GetAllBenefitGroups(req request.BenefitGroupPageReq) *web.JsonResult {
	groups, total, err := r.benefitModel.GetBenefitGroupPage(req.PageSize, req.CurrentPage, "", "")
	if err != nil {
		return web.JsonInternalError(err)
	}

	return web.JsonData(web.PageJsonResult{
		Data:  groups,
		Total: total,
	})
}

// UpdateBenefitGroupStatus 更新权益组状态
func (r *BenefitRepo) UpdateBenefitGroupStatus(req request.UpdateBenefitGroupStatusReq) *web.JsonResult {
	if err := r.benefitModel.UpdateBenefitGroupStatus(req.Id, req.Status); err != nil {
		return web.JsonInternalError(err)
	}

	return web.JsonOK()
}

// GetUserBenefitsList 获取用户权益列表
func (r *BenefitRepo) GetUserBenefitsList(req request.UserBenefitListReq) *web.JsonResult {
	userBenefits, total, err := r.benefitModel.GetUserBenefitsList(req.PageSize, req.CurrentPage, req.Uid, req.BenefitGroupCode, req.Status)
	if err != nil {
		return web.JsonInternalError(err)
	}

	return web.JsonData(web.PageJsonResult{
		Data:  userBenefits,
		Total: total,
	})
}

// GetUserBenefitDetail 获取用户权益详情
func (r *BenefitRepo) GetUserBenefitDetail(req request.UserBenefitDetailReq) *web.JsonResult {
	userBenefit, err := r.benefitModel.GetUserBenefitByID(req.Id)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if userBenefit == nil {
		return web.JsonError("用户权益不存在")
	}
	return web.JsonData(userBenefit)
}

// GetBenefitsByGroupCode 根据权益组编码获取权益列表
func (r *BenefitRepo) GetBenefitsByGroupCode(req request.GetBenefitsByGroupCodeReq) *web.JsonResult {
	benefits, err := r.benefitModel.GetBenefitsByGroupCode(req.Code)
	if err != nil {
		return web.JsonInternalError(err)
	}

	return web.JsonData(benefits)
}
