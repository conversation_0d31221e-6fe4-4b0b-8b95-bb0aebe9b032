package data

import (
	"fmt"
	"loop/internal/model"
	"loop/internal/request"
	"loop/pkg/util"
	"loop/pkg/web"
	"reflect"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/wire"
	"github.com/jinzhu/copier"
	"github.com/sirupsen/logrus"
)

var ProviderSet = wire.NewSet(
	NewUserRepo, NewHomeRepo, NewResourceRepo, NewCategoryRepo, NewSettingRepo,
	NewV1Repo, NewVideoRepo, NewNoteRepo, NewCollectRepo, NewAIRepo, NewDataCenterRepo,
	NewUserAdminRepo, NewSeriesAdminRepo, NewResourceAdminRepo, NewCategoryAdminRepo,
	NewSeriesRepo, NewVipRepo, NewBenefitRepo, NewPlanRepo,
	NewCategoryTypeRepo, NewCategoryTypeAdminRepo,
	NewSpeechEvaluationData, NewFeedbackRepo,
)

func GetList[T any](model *model.DbModel, pageSize int, currentPage int) *web.JsonResult {

	var result []*T
	count, err := model.GetListPage(&result, currentPage, pageSize, "")
	if err != nil {
		return web.JsonInternalError(err)
	}

	return web.JsonData(web.PageJsonResult{
		Data:  result,
		Total: count,
	})
}
func GetListWithResp[T any, R any](model *model.DbModel, pageSize int, currentPage int) *web.JsonResult {
	var result []*T
	count, err := model.GetListPage(&result, currentPage, pageSize, "")
	if err != nil {
		return web.JsonInternalError(err)
	}
	var resps []*R
	copier.Copy(&resps, &result)
	return web.JsonData(web.PageJsonResult{
		Data:  resps,
		Total: count,
	})
}
func DeleteById[T any](model *model.DbModel, id string) *web.JsonResult {
	var instance T
	model.Delete(&instance, id)
	return web.JsonOK()
}
func DeleteMultiId[T any](model *model.DbModel, ids []string) *web.JsonResult {
	if err := util.ValidateIDs(ids, 1000); err != nil {
		return web.JsonError(err.Error())
	}

	var instance T
	if err := model.DeleteMultiWithValidation(&instance, ids); err != nil {
		return web.JsonInternalError(err)
	}

	// 添加审计日志
	logrus.WithFields(logrus.Fields{
		"operation": "DeleteMultiId",
		"ids":       ids,
		"type":      fmt.Sprintf("%T", instance),
		"timestamp": time.Now(),
	}).Info("Batch Delete Operation")

	return web.JsonOK()
}
func UpdateOne[T any, R any](model *model.DbModel, req R) *web.JsonResult {
	var saver T
	if err := copier.Copy(&saver, &req); err != nil {
		return web.JsonInternalError(err)
	}
	idField := "id"
	idValue := reflect.ValueOf(req).FieldByName("Id").Interface()

	if err := model.Update(saver, idField+" = ?", idValue); err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonData(saver)
}
func UpdateOneWithResp[T any, R any, A any](model *model.DbModel, req A) *web.JsonResult {
	var saver T
	if err := copier.Copy(&saver, &req); err != nil {
		return web.JsonInternalError(err)
	}
	idField := "id"
	idValue := reflect.ValueOf(req).FieldByName("Id").Interface()

	if err := model.Update(saver, idField+" = ?", idValue); err != nil {
		return web.JsonInternalError(err)
	}
	var resps R
	copier.Copy(&resps, &saver)
	return web.JsonData(resps)
}

func GetOne[T any](model *model.DbModel, id string) *web.JsonResult {
	var result T
	if found, err := model.GetOne(&result, "id = ?", id); !found {
		if err != nil {
			return web.JsonInternalError(err)
		}
	}
	return web.JsonData(result)
}
func GetOneWithResp[T any, R any](model *model.DbModel, id string) *web.JsonResult {
	var result T
	if found, err := model.GetOne(&result, "id = ?", id); !found {
		if err != nil {
			return web.JsonInternalError(err)
		}
	}
	var resps R
	copier.Copy(&resps, &result)
	return web.JsonData(resps)
}
func SaveOne[T any, R any](model *model.DbModel, c *gin.Context, req R) *web.JsonResult {
	var saver T
	copier.Copy(&saver, &req)
	if err := model.Save(&saver).Error; err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonData(saver)
}

// req的参数一定要 * ，指针类型的  不然会全部覆盖更新
func SaveOneOrUpdate[T any, R any](model *model.DbModel, c *gin.Context, req R, id string) *web.JsonResult {
	if id == "" {
		return SaveOne[T](model, c, req)
	}
	var result T
	if found, err := model.GetOne(&result, "id = ?", id); !found {
		if err != nil {
			return web.JsonInternalError(err)
		}
		return SaveOne[T](model, c, req)
	}
	fmt.Printf("SaveOneOrUpdate SaveOneOrUpdate SaveOneOrUpdate req: %v\n", req)
	copier.Copy(&result, &req)
	return SaveOne[T](model, c, result)
}

func DeleteOne[T any](model *model.DbModel, id string) *web.JsonResult {
	var instance T
	if err := model.Delete(&instance, id).Error; err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonOK()
}

func DeleteMulti[T any](model *model.DbModel, ids []string) *web.JsonResult {
	return DeleteMultiId[T](model, ids)
}

func SetPriority[T any](model *model.DbModel, req request.PriorityReq) *web.JsonResult {
	if req.Id == "" {
		return web.JsonParamErr("ID不能为空")
	}

	var instance T
	if err := model.Model(&instance).Where("id = ?", req.Id).Update("priority", req.Priority).Error; err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonOK()
}
