package data

import (
	"encoding/json"
	"fmt"
	"loop/internal/config"
	"loop/internal/data/util"
	"loop/internal/model"
	"loop/internal/request"
	"loop/internal/response"
	"loop/pkg/enum"
	"loop/pkg/jwtx"
	"loop/pkg/timex"
	"loop/pkg/web"
	"sort"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/sirupsen/logrus"
)

func NewDataCenterRepo(
	model *model.DataCenterModel,
	videoModel *model.VideoModel,
	resourceModel *model.ResourceModel,
	userRepo *UserRepo,
	planModel *model.PlanModel,
	speechEvaluationModel *model.SpeechEvaluationModel,
	config *config.Config,
) *DataCenterRepo {
	return &DataCenterRepo{
		model:                 model,
		userRepo:              userRepo,
		videoModel:            videoModel,
		resourceModel:         resourceModel,
		planModel:             planModel,
		speechEvaluationModel: speechEvaluationModel,
		config:                config,
	}
}

type DataCenterRepo struct {
	model                 *model.DataCenterModel
	userRepo              *UserRepo
	videoModel            *model.VideoModel
	resourceModel         *model.ResourceModel
	planModel             *model.PlanModel
	speechEvaluationModel *model.SpeechEvaluationModel
	config                *config.Config
}

func (s *DataCenterRepo) DataEpisodeUpdate(c *gin.Context, req request.DataEpisodeModLsTimeReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	dataEpisode, err := s.model.GetDataEpisode(uid, req.ResourceId, req.ResourceType)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if dataEpisode != nil {
		dataEpisode.CurrentLsTimes = req.CurrentLsTimes
		if err := s.model.Update(dataEpisode, " id = ?", dataEpisode.Id); err != nil {
			return web.JsonInternalError(err)
		}
	}

	return web.JsonOK()
}
func (s *DataCenterRepo) DataEpisodeEachAdd(c *gin.Context, req request.DataEpisodeEachReq) *web.JsonResult {
	uid := jwtx.GetUid(c)

	// 验证会话数据
	if err := util.ValidateSessionData(req.SessionData); err != nil {
		return web.JsonParamErr(err.Error())
	}

	// 验证本地资源（如果是本地资源类型）
	if req.SessionData.ResourceType == int(enum.LocalResource) {
		localResource, err := s.videoModel.GetUserLocalResourceById(req.SessionData.ResourceId)
		if err != nil {
			return web.JsonInternalError(err)
		}
		if localResource == nil {
			return web.JsonEntityNotFound(c)
		}
	}

	// 处理学习会话数据
	episodeEach, err := util.ProcessLearningSessionData(uid, req)
	if err != nil {
		return web.JsonInternalError(err)
	}

	// 保存学习记录
	if err := s.model.SaveOne(episodeEach); err != nil {
		return web.JsonInternalError(err)
	}

	// 获取或创建数据剧集记录
	dataEpisode, err := s.model.GetDataEpisode(uid, req.SessionData.ResourceId, req.SessionData.ResourceType)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if dataEpisode != nil {
		dataEpisode.CurrentLsTimes = req.SessionData.LsTimes
		if req.SessionData.LsTimes >= dataEpisode.TargetLsTimes {
			//说明完成了LS所有的遍数 获取下当下所有的时长
			singleDataEpisodeResult, err := s.model.GetTotalDataEpisode(uid, dataEpisode.ResourceId, dataEpisode.ResourceType)
			if err != nil {
				return web.JsonInternalError(err)
			}
			dataEpisode.LearnDurationWhenFinish = singleDataEpisodeResult.TotalLearnDuration
			dataEpisode.EndTime = timex.Now().UnixMilli() // 直接获取毫秒时间戳
			dataEpisode.Status = 1
		}
		if err := s.model.Update(dataEpisode, " id = ?", dataEpisode.Id); err != nil {
			return web.JsonInternalError(err)
		}
	}

	return web.JsonOK()
}
func (s *DataCenterRepo) GetEpisodeLsData(c *gin.Context, req request.EpisodeLsDataReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	var episodeName = ""
	if req.ResourceType == int(enum.LocalResource) {
		localResource, err := s.videoModel.GetUserLocalResourceById(req.ResourceId)
		if err != nil {
			return web.JsonInternalError(err)
		}
		if localResource == nil {
			return web.JsonEntityNotFound(c)
		}
		episodeName = localResource.FileName
	} else if req.ResourceType == int(enum.RemoteResouce) {
		resourceRelation, err := s.resourceModel.GetOriginResourceRelation(req.ResourceId)
		if err != nil {
			return web.JsonInternalError(err)
		}
		if resourceRelation == nil {
			return web.JsonEntityNotFound(c)
		}
		episodeName = resourceRelation.Title
	}
	dataEpisode, err := s.model.GetDataEpisode(uid, req.ResourceId, req.ResourceType)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if dataEpisode == nil {
		return web.JsonEntityNotFound(c)
	}
	dpisodeLsDataList, err := s.model.GetEpisodeLsData(uid, req.ResourceId, req.ResourceType)
	if err != nil {
		return web.JsonInternalError(err)
	}

	// Create a map for easy lookup of existing data by LsTimes
	existingDataMap := make(map[int]response.EpisodeLsData)
	for _, data := range dpisodeLsDataList {
		existingDataMap[data.LsTimes] = data
	}

	// Fill in missing data entries up to CurrentLsTimes
	var completeDataList []response.EpisodeLsData
	currentLsTimes := int(dataEpisode.CurrentLsTimes)
	for i := 1; i <= currentLsTimes; i++ {
		if data, exists := existingDataMap[i]; exists {
			completeDataList = append(completeDataList, data)
		} else {
			// Add placeholder for missing data
			completeDataList = append(completeDataList, response.EpisodeLsData{
				LsTimes:            i,
				TotalLearnDuration: 0,
				FinishTime:         0,
			})
		}
	}
	// Sort the completeDataList by LsTimes in descending order
	sort.Slice(completeDataList, func(i, j int) bool {
		return completeDataList[i].LsTimes > completeDataList[j].LsTimes
	})

	var episodesResp response.DataEpisodeResp
	copier.Copy(&episodesResp, &dataEpisode)

	singleDataEpisodeResult, err := s.model.GetTotalDataEpisode(uid, dataEpisode.ResourceId, dataEpisode.ResourceType)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if singleDataEpisodeResult != nil {
		episodesResp.TotalLearnDuration = singleDataEpisodeResult.TotalLearnDuration
		episodesResp.TotalLearnDayTimes = singleDataEpisodeResult.DayCount
	}

	episodesResp.EpisodeName = episodeName

	// 为剧集数据添加计划信息和平均分数
	// 对于GetEpisodeLsData，使用默认的时间范围（最近30天）
	now := time.Now()
	startTime := now.AddDate(0, 0, -30) // 30天前
	endTime := now

	if err := s.enrichEpisodeData(uid, &episodesResp, startTime, endTime); err != nil {
		// 如果获取计划数据失败，记录错误但不影响主流程
		// 这里可以选择记录日志，但不返回错误
	}

	return web.JsonData(response.EpisodeLsDataResp{
		Episodes:      episodesResp,
		EpisodeLsData: completeDataList,
	})

}

func (s *DataCenterRepo) DataEpisodeHome(c *gin.Context, req request.DataEpisodeTypeReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	dataEpisodeList, err := s.model.GetDataEpisodeList(uid)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if len(dataEpisodeList) == 0 {
		//如果当前没有学习的 就返回空的数据
		return web.JsonEmptyData()
	}
	var dailyResps []response.DataEpisodeDailyTodayResp
	var episodesResps []response.DataEpisodeResp //剧集列表数据
	for _, dataEpisode := range dataEpisodeList {
		var episodeName = ""
		if dataEpisode.ResourceType == int(enum.LocalResource) {
			localResource, err := s.videoModel.GetUserLocalResourceById(dataEpisode.ResourceId)
			if err != nil {
				logrus.Infof("GetUserLocalResourceById err: %s, resourceId: %s", err.Error(), dataEpisode.ResourceId)
				return web.JsonInternalError(err)
			}
			if localResource != nil {
				episodeName = localResource.FileName
			}
		} else if dataEpisode.ResourceType == int(enum.RemoteResouce) {
			resourceRelation, err := s.resourceModel.GetOriginResourceRelation(dataEpisode.ResourceId)
			if err != nil {
				logrus.Infof("GetOriginResourceRelation err: %s, resourceId: %s", err.Error(), dataEpisode.ResourceId)
				return web.JsonInternalError(err)
			}
			if resourceRelation == nil {
				logrus.Infof("resourceRelation is nil, resourceId: %s", dataEpisode.ResourceId)
				return web.JsonEntityNotFound(c)
			}
			episodeName = resourceRelation.Title
		}
		if dataEpisode.Status == 0 && req.Type == 1 {
			//学习中的数据
			//获取指定时间戳对应的当天的所有 DataEpisodeDaily
			todayDataEpisodeDailys, err := s.model.GetTodayDataEpisodeDailys(uid, dataEpisode.ResourceId, dataEpisode.ResourceType)
			if err != nil {
				return web.JsonInternalError(err)
			}
			if len(todayDataEpisodeDailys) != 0 {
				var dataEpisodeDailyResp response.DataEpisodeDailyTodayResp
				copier.Copy(&dataEpisodeDailyResp, &dataEpisode)
				dataEpisodeDailyResp.EpisodeName = episodeName
				dataEpisodeDailyResp.TargetLsTimes = dataEpisode.TargetLsTimes
				duration, err := s.calculateTotalLearnDuration(todayDataEpisodeDailys)
				if err != nil {
					dataEpisodeDailyResp.TotalLearnDuration = 0
				} else {
					dataEpisodeDailyResp.TotalLearnDuration = duration
				}
				dailyResps = append(dailyResps, dataEpisodeDailyResp)
			}
		}

		var episodesResp response.DataEpisodeResp
		copier.Copy(&episodesResp, &dataEpisode)

		startTime, endTime := s.getStartEndTime(req.Type, req.StartTime, req.EndTime)
		if req.Type == 4 {
			startOfYear := time.Date(startTime.Year(), 1, 1, 0, 0, 0, 0, startTime.Location())
			endOfYear := time.Date(endTime.Year()+1, 1, 1, 0, 0, 0, 0, startTime.Location())
			startTime = startOfYear
			endTime = endOfYear
		}
		singleDataEpisodeResult, err := s.model.GetTotalDataEpisodeByTime(uid, dataEpisode.ResourceId, dataEpisode.ResourceType, &startTime, &endTime)
		if err != nil {
			return web.JsonInternalError(err)
		}
		if singleDataEpisodeResult != nil {
			episodesResp.TotalLearnDuration = singleDataEpisodeResult.TotalLearnDuration
			episodesResp.TotalLearnDayTimes = singleDataEpisodeResult.DayCount
		}
		episodesResp.EpisodeName = episodeName
		episodesResp.TargetDesc = "没文案"

		// 为剧集数据添加计划信息和平均分数
		if err := s.enrichEpisodeData(uid, &episodesResp, startTime, endTime); err != nil {
			// 如果获取计划数据失败，记录错误但不影响主流程
			// 这里可以选择记录日志，但不返回错误
		}

		episodesResps = append(episodesResps, episodesResp)
	}

	dataEpisodeTotalResp := response.DataEpisodeTotalResp{}
	totalDataEpisodeResult, err := s.model.GetTotalDataEpisode(uid, "", 0)
	if err != nil {
		return web.JsonInternalError(err)
	}

	if totalDataEpisodeResult != nil {
		dataEpisodeTotalResp.TotalLearnDuration = totalDataEpisodeResult.TotalLearnDuration
		dataEpisodeTotalResp.TotalLearnDayTimes = totalDataEpisodeResult.DayCount
		dataEpisodeTotalResp.TotalLearnVideoSize = len(dataEpisodeList)
	}

	resp := &response.DataEpisodeHomeResp{
		Daily:    &dailyResps,
		Total:    &dataEpisodeTotalResp,
		Episodes: &episodesResps,
	}
	return web.JsonData(resp)
}

func (s *DataCenterRepo) DataEpisodeList(c *gin.Context, req request.EmptyReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	dataEpisodeList, err := s.model.GetDataEpisodeList(uid)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if len(dataEpisodeList) == 0 {
		//如果当前没有学习的 就返回空的数据
		return web.JsonEmptyData()
	}
	var episodesResps []response.DataEpisodeResp //剧集列表数据
	for _, dataEpisode := range dataEpisodeList {
		var episodeName = ""
		if dataEpisode.ResourceType == int(enum.LocalResource) {
			localResource, err := s.videoModel.GetUserLocalResourceById(dataEpisode.ResourceId)
			if err != nil {
				return web.JsonInternalError(err)
			}
			if localResource != nil {
				episodeName = localResource.FileName
			}
		} else if dataEpisode.ResourceType == int(enum.RemoteResouce) {
			resourceRelation, err := s.resourceModel.GetOriginResourceRelation(dataEpisode.ResourceId)
			if err != nil {
				return web.JsonInternalError(err)
			}
			if resourceRelation == nil {
				return web.JsonEntityNotFound(c)
			}
			episodeName = resourceRelation.Title
		}

		var episodesResp response.DataEpisodeResp
		copier.Copy(&episodesResp, &dataEpisode)
		totalDataEpisodeResult, err := s.model.GetTotalDataEpisode(uid, dataEpisode.ResourceId, dataEpisode.ResourceType)
		if err != nil {
			return web.JsonInternalError(err)
		}
		if totalDataEpisodeResult != nil {
			episodesResp.TotalLearnDuration = totalDataEpisodeResult.TotalLearnDuration
			episodesResp.TotalLearnDayTimes = totalDataEpisodeResult.DayCount
		}
		episodesResp.EpisodeName = episodeName
		episodesResp.TargetDesc = "没文案"

		// 为剧集数据添加计划信息和平均分数
		// 对于DataEpisodeList，使用默认的时间范围（最近30天）
		now := time.Now()
		startTime := now.AddDate(0, 0, -30) // 30天前
		endTime := now

		if err := s.enrichEpisodeData(uid, &episodesResp, startTime, endTime); err != nil {
			// 如果获取计划数据失败，记录错误但不影响主流程
			// 这里可以选择记录日志，但不返回错误
		}

		episodesResps = append(episodesResps, episodesResp)
	}
	return web.JsonData(episodesResps)
}
func (s *DataCenterRepo) calculateTotalLearnDuration(todayDataEpisode []*model.DataEpisodeEach) (int64, error) {
	return util.CalculateTotalDuration(todayDataEpisode), nil
}

func (s *DataCenterRepo) DataEpisodeByChart(c *gin.Context, req request.DataEpisodeTypeReq) *web.JsonResult {
	if req.Type == 1 {
		return s.DataEpisodeDay(c, req.StartTime, req.EndTime)
	}
	if req.Type == 2 {
		return s.DataEpisodeWeek(c, req.StartTime, req.EndTime)
	}
	if req.Type == 3 {
		return s.DataEpisodeMonth(c, req.StartTime, req.EndTime)
	}
	if req.Type == 4 {
		return s.DataEpisodeYear(c, req.StartTime, req.EndTime)
	}
	return web.JsonEmptyData()

}

func (s *DataCenterRepo) DataEpisodeDay(c *gin.Context, startTime int64, endTime int64) *web.JsonResult {
	uid := jwtx.GetUid(c)
	var totalLearnDuration int64
	hourlyDatas := make([]response.DataEpisodeChartSubResp, 24)
	startOfDay, endOfDay := s.getStartEndTime(1, startTime, endTime)
	dayData, err := s.model.GetDayDataEpisode(uid, startOfDay, endOfDay)
	if err != nil {
		return web.JsonInternalError(err)
	}

	for hourIndex := 0; hourIndex < 24; hourIndex++ {
		episodes := dayData[hourIndex]
		hourDuration, err := s.calculateTotalLearnDuration(episodes)
		if err != nil {
			return web.JsonInternalError(err)
		}
		totalLearnDuration += hourDuration

		currentHour := startOfDay.Add(time.Duration(hourIndex) * time.Hour)
		startDate := currentHour.Unix()
		endDate := currentHour.Add(time.Hour).Unix()

		hourlyDatas[hourIndex] = response.DataEpisodeChartSubResp{
			Duration:        hourDuration,
			StartDate:       startDate,
			EndDate:         endDate,
			StartDateString: s.formatDateString(currentHour, true),
		}
	}
	response := response.DataEpisodeChartResp{
		TotalLearnDuration: totalLearnDuration,
		Datas:              hourlyDatas,
	}

	return web.JsonData(response)
}

func (s *DataCenterRepo) DataEpisodeWeek(c *gin.Context, startTime int64, endTime int64) *web.JsonResult {
	uid := jwtx.GetUid(c)

	var totalLearnDuration int64
	datas := make([]response.DataEpisodeChartSubResp, 7)
	startOfWeek, endOfWeek := s.getStartEndTime(1, startTime, endTime)
	weekData, err := s.model.GetWeekDataEpisode(uid, startOfWeek, endOfWeek)
	if err != nil {
		return web.JsonInternalError(err)
	}

	for dayIndex := 0; dayIndex < 7; dayIndex++ {
		episodes := weekData[dayIndex]
		dayDuration, err := s.calculateTotalLearnDuration(episodes)
		if err != nil {
			return web.JsonInternalError(err)
		}
		totalLearnDuration += dayDuration

		currentDay := startOfWeek.AddDate(0, 0, dayIndex)
		startDate := time.Date(currentDay.Year(), currentDay.Month(), currentDay.Day(), 0, 0, 0, 0, currentDay.Location()).Unix()
		endDate := time.Date(currentDay.Year(), currentDay.Month(), currentDay.Day(), 23, 59, 59, 999999999, currentDay.Location()).Unix()

		datas[dayIndex] = response.DataEpisodeChartSubResp{
			Duration:        dayDuration,
			StartDate:       startDate,
			EndDate:         endDate,
			StartDateString: s.formatDateString(currentDay, false),
			EndDateString:   s.formatDateString(currentDay, false),
		}
	}
	response := response.DataEpisodeChartResp{
		TotalLearnDuration: totalLearnDuration,
		Datas:              datas,
	}

	return web.JsonData(response)
}

func (s *DataCenterRepo) DataEpisodeMonth(c *gin.Context, startTime int64, endTime int64) *web.JsonResult {
	uid := jwtx.GetUid(c)
	start, end := s.getStartEndTime(3, startTime, endTime)
	daysInMonth := time.Date(start.Year(), start.Month()+1, 0, 0, 0, 0, 0, start.Location()).Day()
	dailyDatas := make([]response.DataEpisodeChartSubResp, daysInMonth)
	var totalLearnDuration int64
	monthData, err := s.model.GetMonthDataEpisode(uid, start, end)
	if err != nil {
		return web.JsonInternalError(err)
	}
	for dayIndex := 0; dayIndex < daysInMonth; dayIndex++ {
		episodes := monthData[dayIndex+1]
		dayDuration, err := s.calculateTotalLearnDuration(episodes)
		if err != nil {
			return web.JsonInternalError(err)
		}
		totalLearnDuration += dayDuration

		currentDay := time.Date(start.Year(), start.Month(), dayIndex+1, 0, 0, 0, 0, start.Location())
		startDate := time.Date(currentDay.Year(), currentDay.Month(), currentDay.Day(), 0, 0, 0, 0, currentDay.Location()).Unix()
		endDate := time.Date(currentDay.Year(), currentDay.Month(), currentDay.Day(), 23, 59, 59, 999999999, currentDay.Location()).Unix()

		dailyDatas[dayIndex] = response.DataEpisodeChartSubResp{
			Duration:        dayDuration,
			StartDate:       startDate,
			EndDate:         endDate,
			StartDateString: s.formatDateString(currentDay, false),
			EndDateString:   s.formatDateString(currentDay, false),
		}
	}

	response := response.DataEpisodeChartResp{
		TotalLearnDuration: totalLearnDuration,
		Datas:              dailyDatas,
	}

	return web.JsonData(response)
}
func (s *DataCenterRepo) DataEpisodeYear(c *gin.Context, startTime int64, endTime int64) *web.JsonResult {
	uid := jwtx.GetUid(c)

	monthlyDatas := make([]response.DataEpisodeChartSubResp, 12)
	var totalLearnDuration int64
	start, end := s.getStartEndTime(4, startTime, endTime)
	startOfYear := time.Date(start.Year(), 1, 1, 0, 0, 0, 0, start.Location())
	endOfYear := time.Date(end.Year()+1, 1, 1, 0, 0, 0, 0, start.Location())

	yearData, err := s.model.GetYearDataEpisode(uid, startOfYear, endOfYear)
	if err != nil {
		return web.JsonInternalError(err)
	}

	for monthIndex := 0; monthIndex < 12; monthIndex++ {
		episodes := yearData[monthIndex+1]
		monthDuration, err := s.calculateTotalLearnDuration(episodes)
		if err != nil {
			return web.JsonInternalError(err)
		}
		totalLearnDuration += monthDuration

		currentMonth := time.Date(start.Year(), time.Month(monthIndex+1), 1, 0, 0, 0, 0, start.Location())
		startDate := time.Date(currentMonth.Year(), currentMonth.Month(), 1, 0, 0, 0, 0, currentMonth.Location()).Unix()
		endDate := time.Date(currentMonth.Year(), currentMonth.Month()+1, 0, 23, 59, 59, 999999999, currentMonth.Location()).Unix()

		monthlyDatas[monthIndex] = response.DataEpisodeChartSubResp{
			Duration:        monthDuration,
			StartDate:       startDate,
			EndDate:         endDate,
			StartDateString: s.formatDateString(currentMonth, false),
			EndDateString:   s.formatDateString(currentMonth.AddDate(0, 1, -1), false),
		}
	}

	response := response.DataEpisodeChartResp{
		TotalLearnDuration: totalLearnDuration,
		Datas:              monthlyDatas,
	}

	return web.JsonData(response)
}

func (s *DataCenterRepo) formatDateString(t time.Time, isDayData bool) string {
	return util.FormatDateString(t, isDayData)
}
func (s *DataCenterRepo) getStartEndTime(timeType int, startTime int64, endTime int64) (time.Time, time.Time) {
	timeRange := util.GetTimeRange(timeType, startTime, endTime)
	return timeRange.Start, timeRange.End
}

// getEpisodePlanData 获取剧集的计划数据（CurrentSentences和TargetSentences）
func (s *DataCenterRepo) getEpisodePlanData(uid string, resourceId string, startTime, endTime time.Time) (int64, int64, error) {
	// 从LearningPlanDay表中获取当前用户的该资源的学习计划数据，在指定时间范围内
	var planDays []model.LearningPlanDay

	// 查询在时间范围内且包含该资源的所有计划天数据
	err := s.planModel.Model(&model.LearningPlanDay{}).
		Where("uid = ? AND JSON_SEARCH(resource_ids, 'one', ?) IS NOT NULL AND study_timestamp >= ? AND study_timestamp <= ?",
			uid, resourceId, startTime.UnixMilli(), endTime.UnixMilli()).
		Find(&planDays).Error

	if err != nil {
		return 0, 0, fmt.Errorf("获取学习计划数据失败: %w", err)
	}

	if len(planDays) == 0 {
		// 如果没有找到计划数据，返回默认值
		return 0, 0, nil
	}

	// 统计所有匹配记录的TargetSentences和CurrentSentences总和
	var totalCurrentSentences int64
	var totalTargetSentences int64

	for _, planDay := range planDays {
		totalCurrentSentences += int64(planDay.CurrentSentences)
		totalTargetSentences += int64(planDay.TargetSentences)
	}

	return totalCurrentSentences, totalTargetSentences, nil
}

// getEpisodeAverageScore 获取剧集的平均分数
func (s *DataCenterRepo) getEpisodeAverageScore(uid string, resourceId string, startTime, endTime time.Time) (int64, error) {
	// 从SpeechEvaluation表中获取该用户和资源的所有评测记录，在指定时间范围内
	var evaluations []*model.SpeechEvaluation
	err := s.speechEvaluationModel.Model(&model.SpeechEvaluation{}).
		Where("uid = ? AND resource_id = ? AND created_at >= ? AND created_at <= ?",
			uid, resourceId, startTime, endTime).
		Find(&evaluations).Error
	if err != nil {
		return 0, fmt.Errorf("获取语音评测数据失败: %w", err)
	}

	if len(evaluations) == 0 {
		return 0, nil
	}

	var totalScore int64
	var validCount int64

	for _, evaluation := range evaluations {
		// 解析Content字段中的JSON数据
		var contentData map[string]interface{}
		if err := json.Unmarshal([]byte(evaluation.Content), &contentData); err != nil {
			// 如果解析失败，跳过这条记录
			continue
		}

		// 从result字段中获取overall分数
		if result, ok := contentData["result"].(map[string]interface{}); ok {
			if overall, ok := result["overall"].(float64); ok {
				totalScore += int64(overall)
				validCount++
			}
		}
	}

	if validCount == 0 {
		return 0, nil
	}

	return totalScore / validCount, nil
}

// enrichEpisodeData 为剧集数据添加计划信息和平均分数
func (s *DataCenterRepo) enrichEpisodeData(uid string, episodeResp *response.DataEpisodeResp, startTime, endTime time.Time) error {
	// 获取计划数据
	currentSentences, targetSentences, err := s.getEpisodePlanData(uid, episodeResp.ResourceId, startTime, endTime)
	if err != nil {
		return fmt.Errorf("获取计划数据失败: %w", err)
	}

	// 获取平均分数
	averageScore, err := s.getEpisodeAverageScore(uid, episodeResp.ResourceId, startTime, endTime)
	if err != nil {
		return fmt.Errorf("获取平均分数失败: %w", err)
	}

	// 设置数据
	episodeResp.CurrentSentences = currentSentences
	episodeResp.TargetSentences = targetSentences
	episodeResp.AverageScore = averageScore

	return nil
}
