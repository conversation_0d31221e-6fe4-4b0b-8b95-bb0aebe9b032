package data

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"loop/internal/client"
	"loop/internal/config"
	"loop/internal/data/types"
	"loop/internal/data/util"
	"loop/internal/model"
	"loop/internal/request"
	"loop/internal/response"
	"loop/pkg/dbx"
	"loop/pkg/enum"
	"loop/pkg/jwtx"
	"loop/pkg/oss"
	"loop/pkg/timex"
	"loop/pkg/web"
	"math"
	"os"
	"sort"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/sirupsen/logrus"
	"github.com/thoas/go-funk"
	"go4.org/syncutil/singleflight"
)

func NewPlanRepo(
	model *model.PlanModel,
	resourceModel *model.ResourceModel,
	config *config.Config,
	client *client.Client,
	dataCenterRepo *DataCenterRepo,
) *PlanRepo {
	return &PlanRepo{
		model:          model,
		resourceModel:  resourceModel,
		config:         config,
		client:         client,
		dataCenterRepo: dataCenterRepo,
	}
}

type PlanRepo struct {
	model          *model.PlanModel
	resourceModel  *model.ResourceModel
	config         *config.Config
	client         *client.Client
	dataCenterRepo *DataCenterRepo
}

// GetQuestionnaire 获取用户的问卷信息
func (r *PlanRepo) GetQuestionnaire(c *gin.Context) *web.JsonResult {
	uid := jwtx.GetUid(c)

	questionnaire, err := r.model.GetUserQuestionnaire(uid)
	if err != nil {
		logrus.WithError(err).Error("Failed to get user questionnaire")
		return web.JsonInternalError(err)
	}

	if questionnaire == nil {
		return web.JsonEntityNotFound(c)
	}

	resp := &response.UserQuestionnaireResp{}
	copier.Copy(resp, questionnaire)
	resp.CreatedAt = timex.FormatRFC3339(questionnaire.CreatedAt)
	resp.UpdatedAt = timex.FormatRFC3339(questionnaire.UpdatedAt)

	return web.JsonData(resp)
}

// GeneratePlan 为用户生成学习计划
// 添加 singleflight 组
var generatePlanGroup singleflight.Group

// 全局状态映射，用于跟踪计划生成状态
var planGenerationStatusMap = sync.Map{}

// 检查是否有正在进行的计划生成
func (r *PlanRepo) isGeneratingPlan(uid string) bool {
	_, ok := planGenerationStatusMap.Load(uid)
	return ok
}

// 标记计划生成开始
func (r *PlanRepo) markPlanGenerationStarted(uid string) {
	planGenerationStatusMap.Store(uid, true)
}

// 标记计划生成结束
func (r *PlanRepo) markPlanGenerationCompleted(uid string) {
	planGenerationStatusMap.Delete(uid)
}

func (r *PlanRepo) GeneratePlan(c *gin.Context, req request.GeneratePlanReq) *web.JsonResult {
	uid := jwtx.GetUid(c)

	// 检查用户是否已有活跃计划
	existingPlan, err := r.model.GetActiveLearningPlan(uid)
	if err != nil {
		logrus.WithError(err).Error("获取用户活跃计划失败 uid:", uid)
		return web.JsonInternalError(err)
	}

	// 如果用户已有活跃计划且不强制重新生成，则返回错误
	if existingPlan != nil && !req.ForceRegenerate {
		return web.JsonParamErr("用户已有活跃计划，如需重新生成请设置forceRegenerate=true")
	}

	// 检查是否已经有正在进行的计划生成
	if r.isGeneratingPlan(uid) {
		// 如果已经有正在进行的计划生成，返回状态信息
		return web.JsonData(map[string]any{
			"message": "计划生成正在进行中，请稍后查询",
			"status":  "processing",
		})
	}

	// 如果存在活跃计划，立即将其状态设置为无效
	if existingPlan != nil {
		// 使用事务将现有计划标记为无效
		err = r.model.Tx(func(txDb *dbx.DBExtension) error {
			return txDb.Model(&model.LearningPlan{}).Where(model.LearningPlan{Uid: uid, Status: 1}).Update("status", 0).Error
		})

		if err != nil {
			logrus.WithError(err).Error("使现有计划失效失败 uid:", uid)
			return web.JsonInternalError(err)
		}

		logrus.Infof("用户 %s 的现有计划已设置为无效", uid)
	}

	// 创建临时问卷对象用于AI服务调用和保存
	tempQuestionnaire := &model.UserQuestionnaire{
		Uid:               uid,
		CurrentLevel:      req.CurrentLevel,
		TargetLevel:       req.TargetLevel,
		DailyStudyMinutes: req.DailyStudyMinutes,
		MotivationSource:  req.MotivationSource,
		DesiredAbility:    req.DesiredAbility,
	}

	// 标记计划生成开始
	r.markPlanGenerationStarted(uid)

	// 获取目标语言代码，因为在goroutine中无法访问gin上下文
	// targetLangCode := jwtx.GetTargetLangCode(c)

	// 在后台goroutine中执行计划生成
	go func() {
		// 使用 singleflight 确保同一用户的并发请求只执行一次
		key := fmt.Sprintf("generate_plan_%s", uid)

		// 使用 singleflight 执行生成计划的逻辑
		result, err := generatePlanGroup.Do(key, func() (any, error) {
			logrus.Infof("开始为用户 %s 生成学习计划", uid)

			// 获取AI使用的资源
			// 首先获取与目标语言相关的资源关系
			var resourceRelations []*model.ResourceRelation
			if err := r.resourceModel.GetList(&resourceRelations, ""); err != nil {
				// if err := r.resourceModel.GetList(&resourceRelations, model.ResourceRelation{LangCode: targetLangCode}); err != nil {
				logrus.WithError(err).Error("获取资源关系失败 uid:", uid)
				return nil, err
			}

			// 提取资源ID
			var resourceIds []string
			for _, relation := range resourceRelations {
				resourceIds = append(resourceIds, relation.ResourceId)
			}

			// 如果没有找到资源关系，返回空列表
			if len(resourceIds) == 0 {
				return &types.PlanAIResponse{}, nil
			}

			// 获取资源
			resources, err := r.resourceModel.GetByIds(resourceIds)
			if err != nil {
				logrus.WithError(err).Error("获取资源失败 uid:", uid)
				return nil, err
			}

			// 创建一个新的上下文，因为原始的gin上下文可能已经关闭
			ctx := context.Background()

			// 过滤掉tags为空的资源
			validResources := util.FilterEmptyTagsResources(resources)

			// 先调用AI服务筛选资源
			logrus.Info("开始筛选资源...")
			filteredResources, err := r.callFilterResourcesAIService(ctx, tempQuestionnaire, validResources)
			if err != nil {
				logrus.WithError(err).Error("筛选资源失败 uid:", uid)
				return nil, err
			}

			// 准备资源信息
			resourceInfos := util.PrepareResourceInfos(filteredResources)

			// 调用分阶段生成计划的函数
			aiResponse, err := r.callGenerateStagedPlanAIService(ctx, tempQuestionnaire, resourceInfos)
			if err != nil {
				logrus.WithError(err).Error("生成分阶段计划失败 uid:", uid)
				return nil, err
			}

			return aiResponse, nil

		})

		// 无论成功还是失败，最后都要清理状态
		defer r.markPlanGenerationCompleted(uid)

		if err != nil {
			logrus.WithError(err).Error("生成计划失败 uid:", uid)
			return
		}

		// 类型断言，将 result 转换为 *types.PlanAIResponse
		aiResponse, ok := result.(*types.PlanAIResponse)
		if !ok {
			err := errors.New("类型断言失败，无法将结果转换为 PlanAIResponse")
			logrus.Error(err)
			return
		}
		// 打印AI响应内容
		jsonBytes, err := json.MarshalIndent(aiResponse, "", "  ")
		if err != nil {
			logrus.WithError(err).Error("序列化AI响应失败")
			return
		}
		logrus.Infof("AI生成的计划响应: %s", string(jsonBytes))
		if !ok {
			err := errors.New("类型断言失败，无法将结果转换为 PlanAIResponse")
			logrus.Error(err)
			return
		}

		// 计算结束时间
		var endDate time.Time

		// 如果有阶段，使用最后一个阶段的结束时间
		if len(aiResponse.Stages) > 0 {
			lastStage := aiResponse.Stages[len(aiResponse.Stages)-1]
			if lastStage.EndDate != "" {
				parsedEndDate, err := timex.ParseStandard(lastStage.EndDate)
				if err == nil {
					endDate = parsedEndDate
				}
			}
		}

		// 使用事务处理保存问卷和创建计划
		err = r.model.Tx(func(txDb *dbx.DBExtension) error {
			// 1. 获取用户问卷（如果存在）
			existingQuestionnaire, err := r.model.GetUserQuestionnaire(uid)
			if err != nil {
				return err
			}

			var questionnaireId string
			if existingQuestionnaire == nil {
				// 2. 创建新问卷
				newQuestionnaire := &model.UserQuestionnaire{
					Uid:               uid,
					MotivationSource:  tempQuestionnaire.MotivationSource,
					DesiredAbility:    tempQuestionnaire.DesiredAbility,
					CurrentLevel:      req.CurrentLevel,
					TargetLevel:       req.TargetLevel,
					DailyStudyMinutes: req.DailyStudyMinutes,
				}

				// 使用事务保存问卷
				if err := txDb.Create(newQuestionnaire).Error; err != nil {
					logrus.WithError(err).Error("保存用户问卷失败 uid:", uid)
					return err
				}
				questionnaireId = newQuestionnaire.Id
			} else {
				questionnaireId = existingQuestionnaire.Id
			}

			// 3.2 创建新计划
			now := timex.Now()
			plan := &model.LearningPlan{
				Uid:             uid,
				StartLevel:      req.CurrentLevel,
				TargetLevel:     req.TargetLevel,
				StartTimestamp:  timex.GetDateZeroTimestamp(now),
				EndTimestamp:    timex.GetDateZeroTimestamp(endDate),
				Status:          1,
				QuestionnaireId: questionnaireId,
			}

			if err := txDb.Create(plan).Error; err != nil {
				return err
			}

			// 3.3 创建计划阶段
			for i, stageResponse := range aiResponse.Stages {

				// 收集阶段的资源ID
				var resourceIds []string

				// 如果有周数据，从周数据中收集资源
				if len(stageResponse.Weeks) > 0 && len(stageResponse.Weeks[0].Resources) > 0 {
					for _, resourceResponse := range stageResponse.Weeks[0].Resources {
						resourceIds = append(resourceIds, resourceResponse.ResourceId)
					}
				}

				// 将资源ID列表转换为JSON字符串
				resourceIdsJSON, err := json.Marshal(resourceIds)
				if err != nil {
					logrus.WithError(err).Error("序列化资源ID列表失败")
					return err
				}

				stage := &model.LearningPlanStage{
					PlanId:      plan.Id,
					StageDesc:   stageResponse.StageDesc, // 使用AI返回的阶段描述
					Objective:   stageResponse.Objective,
					SortOrder:   i + 1,
					ResourceIds: string(resourceIdsJSON), // 保存资源ID列表
				}

				if err := txDb.Create(stage).Error; err != nil {
					return err
				}
			}

			return nil
		})

		if err != nil {
			logrus.WithError(err).Error("创建计划失败 uid:", uid)
			return
		}

		logrus.Infof("用户 %s 的学习计划生成完成", uid)
	}()

	// 立即返回，告知用户计划生成已开始
	message := "计划生成已开始，请稍后查询"
	if existingPlan != nil {
		message = "现有计划已失效，新计划生成已开始，请稍后查询"
	}

	return web.JsonData(map[string]any{
		"message": message,
		"status":  "processing",
	})
}

// GetPlan 获取用户当前活跃的学习计划
func (r *PlanRepo) GetPlan(c *gin.Context, req request.EmptyReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	logrus.Infof("[BUSINESS] GetPlan 开始处理, uid=%s", uid)

	var plan *model.LearningPlan
	var err error
	logrus.Infof("[BUSINESS] GetPlan 获取用户活跃计划中...")
	plan, err = r.model.GetActiveLearningPlan(uid)
	if err != nil {
		logrus.WithError(err).Error("[BUSINESS] GetPlan 获取活跃计划失败 uid:", uid)
		return web.JsonInternalError(err)
	}

	if plan == nil {
		logrus.Infof("[BUSINESS] GetPlan 用户没有活跃计划, uid=%s", uid)
		return web.JsonEmptyData()
	}

	logrus.Infof("[BUSINESS] GetPlan 找到活跃计划, plan_id=%s, 学习日期=%v", plan.Id, plan.StudyDaysOfWeek)

	// 1.1 获取到计划后，先进行日期检查和自动调整
	logrus.Infof("[BUSINESS] GetPlan 开始日期检查和自动调整...")
	err = r.checkAndUpdatePlanDates(plan)
	if err != nil {
		logrus.WithError(err).Warn("[BUSINESS] GetPlan 自动调整计划日期失败，继续返回计划数据 uid:", uid)
		// 注意：这里只记录警告，不中断流程，确保用户仍能看到计划
	} else {
		logrus.Infof("[BUSINESS] GetPlan 日期检查和自动调整完成")
	}

	// 2. 数据收集阶段：一次性获取所有需要的数据
	logrus.Infof("[BUSINESS] GetPlan 开始数据收集阶段...")
	planData, err := r.collectPlanData(plan.Id, uid)
	if err != nil {
		logrus.WithError(err).Error("[BUSINESS] GetPlan 数据收集失败")
		return web.JsonInternalError(err)
	}

	// 3. 获取资源信息映射
	logrus.Infof("[BUSINESS] GetPlan 获取资源信息映射中, 资源数=%d", len(planData.AllResourceIds))
	resourceMap, relationMap, err := r.getResourceMaps(planData.AllResourceIds, jwtx.GetTargetLangCode(c), uid)
	if err != nil {
		logrus.WithError(err).Error("[BUSINESS] GetPlan 获取资源信息映射失败")
		return web.JsonInternalError(err)
	}
	logrus.Infof("[BUSINESS] GetPlan 资源信息映射获取完成, 资源数=%d, 关系数=%d",
		len(resourceMap), len(relationMap))

	// 4. 构建响应
	logrus.Infof("[BUSINESS] GetPlan 开始构建响应...")
	resp := &response.LearningPlanResp{
		Id:              plan.Id,
		StartLevel:      plan.StartLevel,
		TargetLevel:     plan.TargetLevel,
		StartTimestamp:  plan.StartTimestamp,
		EndTimestamp:    plan.EndTimestamp,
		Status:          plan.Status,
		StudyDaysOfWeek: &plan.StudyDaysOfWeek,
		DailySentences:  plan.DailySentences,
		TotalLearnDays:  plan.TotalLearnDays,
		Stages:          r.buildStageResponses(uid, planData, resourceMap, relationMap),
	}

	// 计算当前服务器时间的0点时间戳
	resp.CurrentDayTimestamp = timex.GetTodayZeroTimestamp()

	logrus.Infof("[BUSINESS] GetPlan 响应构建完成, uid=%s, plan_id=%s", uid, plan.Id)
	return web.JsonData(resp)
}

// planDataCollection 收集的计划数据结构
type planDataCollection struct {
	Stages         []*model.LearningPlanStage
	WeeksByStage   map[string][]*model.LearningPlanWeek
	DaysByWeek     map[string][]*model.LearningPlanDay
	AllResourceIds map[string]bool
	AllDays        []*model.LearningPlanDay
}

// collectPlanData 收集计划相关的所有数据
func (r *PlanRepo) collectPlanData(planId, uid string) (*planDataCollection, error) {
	// 1. 获取计划阶段
	stages, err := r.model.GetPlanStages(planId)
	if err != nil {
		logrus.WithError(err).Error("获取计划阶段失败 uid:", uid)
		return nil, err
	}

	// 2. 收集所有资源ID（从阶段和天数据中）
	allResourceIds := make(map[string]bool)

	// 从阶段收集资源ID
	for _, stage := range stages {
		if stage.ResourceIds != "" {
			var stageResourceIds []string
			if err := json.Unmarshal([]byte(stage.ResourceIds), &stageResourceIds); err != nil {
				logrus.WithError(err).Error("解析阶段资源ID列表失败 uid:", uid)
				return nil, err
			}
			for _, resourceId := range stageResourceIds {
				allResourceIds[resourceId] = true
			}
		}
	}

	// 3. 收集所有周数据
	weeksByStage := make(map[string][]*model.LearningPlanWeek)
	allWeekIds := make([]string, 0)

	for _, stage := range stages {
		weeks, err := r.model.GetPlanWeeks(stage.Id)
		if err != nil {
			logrus.WithError(err).Error("获取阶段周数据失败 uid:", uid)
			return nil, err
		}
		weeksByStage[stage.Id] = weeks
		for _, week := range weeks {
			allWeekIds = append(allWeekIds, week.Id)
		}
	}

	// 4. 批量获取所有天数据
	allDays, err := r.model.GetWeeksDays(allWeekIds)
	if err != nil {
		logrus.WithError(err).Error("批量获取天数据失败 uid:", uid)
		return nil, err
	}

	// 5. 按周ID分组天数据，并收集天的资源ID
	daysByWeek := make(map[string][]*model.LearningPlanDay)
	logrus.Infof("collectPlanData - 总天数据数量: %d", len(allDays))
	for _, day := range allDays {
		logrus.Infof("collectPlanData - 添加天数据到周: weekId=%s, dayId=%s, planDayNumber=%d", day.WeekId, day.Id, day.PlanDayNumber)
		daysByWeek[day.WeekId] = append(daysByWeek[day.WeekId], day)

		// 收集天的资源ID
		if day.ResourceIds != "" {
			var dayResourceIds []string
			if err := json.Unmarshal([]byte(day.ResourceIds), &dayResourceIds); err != nil {
				logrus.WithError(err).Error("解析日资源ID失败 uid:", uid)
				return nil, err
			}
			for _, resourceId := range dayResourceIds {
				allResourceIds[resourceId] = true
			}
		}
	}

	return &planDataCollection{
		Stages:         stages,
		WeeksByStage:   weeksByStage,
		DaysByWeek:     daysByWeek,
		AllResourceIds: allResourceIds,
		AllDays:        allDays,
	}, nil
}

// getResourceMaps 获取资源和关系映射
func (r *PlanRepo) getResourceMaps(allResourceIds map[string]bool, langCode, uid string) (map[string]*model.Resource, map[string]*model.ResourceRelation, error) {
	if len(allResourceIds) == 0 {
		return make(map[string]*model.Resource), make(map[string]*model.ResourceRelation), nil
	}

	resourceIds := make([]string, 0, len(allResourceIds))
	for resourceId := range allResourceIds {
		resourceIds = append(resourceIds, resourceId)
	}

	resources, relations, err := r.resourceModel.GetResourcesAndRelations(resourceIds, langCode)
	if err != nil {
		logrus.WithError(err).Error("批量获取资源和关系失败 uid:", uid)
		return nil, nil, err
	}

	// 创建映射
	resourceMap := make(map[string]*model.Resource)
	relationMap := make(map[string]*model.ResourceRelation)

	for _, resource := range resources {
		resourceMap[resource.Id] = resource
	}

	for _, relation := range relations {
		relationMap[relation.ResourceId] = relation
	}

	return resourceMap, relationMap, nil
}

// buildStageResponses 构建阶段响应数据
func (r *PlanRepo) buildStageResponses(uid string, planData *planDataCollection, resourceMap map[string]*model.Resource, relationMap map[string]*model.ResourceRelation) []response.PlanStageResp {
	stageResponses := make([]response.PlanStageResp, 0, len(planData.Stages))

	for _, stage := range planData.Stages {
		stageResp := response.PlanStageResp{
			Id:        stage.Id,
			StageDesc: stage.StageDesc,
			Objective: stage.Objective,
			Weeks:     r.buildWeekResponses(uid, stage.Id, planData, resourceMap, relationMap),
		}
		stageResponses = append(stageResponses, stageResp)
	}

	return stageResponses
}

// buildWeekResponses 构建周响应数据
func (r *PlanRepo) buildWeekResponses(uid string, stageId string, planData *planDataCollection, resourceMap map[string]*model.Resource, relationMap map[string]*model.ResourceRelation) []response.PlanWeekResp {
	weeks := planData.WeeksByStage[stageId]
	weekResponses := make([]response.PlanWeekResp, 0, len(weeks))

	for _, week := range weeks {
		weekResp := response.PlanWeekResp{
			WeekNumber: week.WeekNumber,
			Days:       r.buildDayResponses(uid, week.Id, planData, resourceMap, relationMap),
		}
		weekResponses = append(weekResponses, weekResp)
	}

	return weekResponses
}

// buildDayResponses 构建天响应数据
func (r *PlanRepo) buildDayResponses(uid string, weekId string, planData *planDataCollection, resourceMap map[string]*model.Resource, relationMap map[string]*model.ResourceRelation) []response.PlanDayResp {
	days := planData.DaysByWeek[weekId]
	logrus.Infof("buildDayResponses - weekId: %s, 天数据数量: %d", weekId, len(days))
	dayResponses := make([]response.PlanDayResp, 0, len(days))

	// 获取当前服务器时间
	todayTimestamp := timex.GetTodayZeroTimestamp()

	// 检查当前周是否包含今天
	hasToday := false
	for _, day := range days {
		dayResp := response.PlanDayResp{
			Id:               day.Id,
			PlanDayNumber:    day.PlanDayNumber,
			WeekDayNumber:    day.WeekDayNumber,
			Status:           day.Status,
			StudyTimestamp:   day.StudyTimestamp,
			CurrentSentences: day.CurrentSentences,
			TargetSentences:  day.TargetSentences,
			Type:             int(enum.StudyDay), // 默认为学习日
			Resources:        r.buildDayResourceResponses(day.ResourceIds, resourceMap, relationMap),
		}

		// 检查是否是今天
		if day.StudyTimestamp == todayTimestamp {
			hasToday = true
		}

		// 检查是否是今天，只有今天的数据才添加平均分数和总学习时长
		if day.StudyTimestamp == todayTimestamp {
			// 解析资源ID（暂时只有一个）
			var resourceIds []string
			if day.ResourceIds != "" {
				if err := json.Unmarshal([]byte(day.ResourceIds), &resourceIds); err != nil {
					logrus.WithError(err).Warnf("解析天资源ID失败, dayId=%s", day.Id)
				}
			}

			// 为今天的天数据添加平均分数和总学习时长
			if len(resourceIds) > 0 {
				// 计算该天的时间范围（从0点到23:59:59）
				dayStart := timex.TimestampToTime(day.StudyTimestamp)
				dayEnd := dayStart.Add(24 * time.Hour).Add(-time.Millisecond)

				if err := r.enrichPlanDayData(uid, &dayResp, resourceIds[0], dayStart, dayEnd); err != nil {
					logrus.WithError(err).Warnf("为天数据添加平均分数和总学习时长失败, dayId=%s", day.Id)
				}
			}
		}

		dayResponses = append(dayResponses, dayResp)
	}

	// 如果当前周包含今天但没有今天的学习计划，则创建一个休息日的计划
	if !hasToday {
		// 找到当前周信息
		var currentWeek *model.LearningPlanWeek
		for _, stage := range planData.Stages {
			weeks := planData.WeeksByStage[stage.Id]
			for _, week := range weeks {
				if week.Id == weekId {
					currentWeek = week
					break
				}
			}
			if currentWeek != nil {
				break
			}
		}

		if currentWeek != nil {
			// 检查今天是否在当前周的时间范围内
			weekStart := timex.TimestampToTime(currentWeek.StartTimestamp)
			weekEnd := timex.TimestampToTime(currentWeek.EndTimestamp)
			today := timex.TimestampToTime(todayTimestamp)

			if (today.Equal(weekStart) || today.After(weekStart)) && (today.Equal(weekEnd) || today.Before(weekEnd)) {
				// 今天在当前周范围内，创建一个休息日的计划
				restDayResp := response.PlanDayResp{
					StudyTimestamp: todayTimestamp,
					Type:           int(enum.RestDay), // 休息日
					Resources:      []response.PlanDayResourceResp{},
				}
				dayResponses = append(dayResponses, restDayResp)
				logrus.Infof("buildDayResponses - 为当前周添加休息日计划, weekId=%s, today=%s", weekId, today.Format("2006-01-02"))
			}
		}
	}

	return dayResponses
}

// enrichPlanDayData 为计划天数据添加平均分数和总学习时长
func (r *PlanRepo) enrichPlanDayData(uid string, dayResp *response.PlanDayResp, resourceId string, startTime, endTime time.Time) error {
	// 使用 datacenter 的方法获取平均分数
	averageScore, err := r.dataCenterRepo.getEpisodeAverageScore(uid, resourceId, startTime, endTime)
	if err != nil {
		logrus.WithError(err).Warnf("获取资源 %s 的平均分数失败", resourceId)
		dayResp.AverageScore = 0
	} else {
		dayResp.AverageScore = averageScore
	}

	// 使用 datacenter 的方法获取学习时长
	learnDuration, err := r.getPlanDayLearnDuration(uid, resourceId, startTime, endTime)
	if err != nil {
		logrus.WithError(err).Warnf("获取资源 %s 的学习时长失败", resourceId)
		dayResp.TotalLearnDuration = 0
	} else {
		dayResp.TotalLearnDuration = learnDuration
	}

	return nil
}

// getPlanDayLearnDuration 获取计划天的学习时长
func (r *PlanRepo) getPlanDayLearnDuration(uid string, resourceId string, startTime, endTime time.Time) (int64, error) {
	result, err := r.dataCenterRepo.model.GetTotalDataEpisodeByTime(uid, resourceId, 0, &startTime, &endTime)
	if err != nil {
		return 0, err
	}
	if result == nil {
		return 0, nil
	}
	return result.TotalLearnDuration, nil
}

// buildDayResourceResponses 构建天资源响应数据
func (r *PlanRepo) buildDayResourceResponses(resourceIdsJson string, resourceMap map[string]*model.Resource, relationMap map[string]*model.ResourceRelation) []response.PlanDayResourceResp {
	if resourceIdsJson == "" {
		return []response.PlanDayResourceResp{}
	}

	var dayResourceIds []string
	if err := json.Unmarshal([]byte(resourceIdsJson), &dayResourceIds); err != nil {
		logrus.WithError(err).Error("解析日资源ID失败")
		return []response.PlanDayResourceResp{}
	}

	resourceResponses := make([]response.PlanDayResourceResp, 0, len(dayResourceIds))
	for _, resourceId := range dayResourceIds {
		dayResourceResp := response.PlanDayResourceResp{
			ResourceId: resourceId,
		}

		if resource, ok := resourceMap[resourceId]; ok {
			dayResourceResp.ResourceCover = resource.Cover
			dayResourceResp.ResourceUrl = oss.GetOssSignedURL(r.client, resource.VideoURL)
		}

		if relation, ok := relationMap[resourceId]; ok {
			dayResourceResp.ResourceName = relation.Title
		}

		resourceResponses = append(resourceResponses, dayResourceResp)
	}

	return resourceResponses
}

// InvalidatePlan 使用户的学习计划失效
func (r *PlanRepo) InvalidatePlan(c *gin.Context, req request.InvalidatePlanReq) *web.JsonResult {
	uid := jwtx.GetUid(c)

	if req.PlanId != "" {
		// 如果提供了计划ID，则只使该计划失效
		plan := &model.LearningPlan{Model: model.Model{Id: req.PlanId}}
		found, err := r.model.GetOne(plan, *plan)
		if err != nil {
			logrus.WithError(err).Error("获取指定计划失败")
			return web.JsonInternalError(err)
		}
		if !found {
			return web.JsonErrorCodeMsg(404, "未找到指定的计划")
		}
		// 验证计划是否属于当前用户
		if plan.Uid != uid {
			return web.JsonErrorCodeMsg(403, "无权操作该计划")
		}
		// 使计划失效
		plan.Status = 0
		if err := r.model.Update(plan, "id = ?", plan.Id); err != nil {
			logrus.WithError(err).Error("使计划失效失败 uid:", uid)
			return web.JsonInternalError(err)
		}
	} else {
		// 否则使用户所有活跃计划失效
		if err := r.model.InvalidateUserPlans(uid); err != nil {
			logrus.WithError(err).Error("使用户计划失效失败 uid:", uid)
			return web.JsonInternalError(err)
		}
	}

	return web.JsonOK()
}

// GenerateFixedPlan 生成固定的学习计划
func (r *PlanRepo) GenerateFixedPlan(c *gin.Context, req request.GenerateFixedPlanReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	targetLangCode := jwtx.GetTargetLangCode(c)
	logrus.Infof("[BUSINESS] GenerateFixedPlan 开始处理, uid=%s, 目标语言=%s, 参数=%+v",
		uid, targetLangCode, req)

	// 检查用户是否已有活跃计划
	logrus.Infof("[BUSINESS] GenerateFixedPlan 检查用户是否已有活跃计划...")
	existingPlan, err := r.model.GetActiveLearningPlan(uid)
	if err != nil {
		logrus.WithError(err).Error("[BUSINESS] GenerateFixedPlan 获取用户活跃计划失败 uid:", uid)
		return web.JsonInternalError(err)
	}

	if existingPlan != nil {
		logrus.Infof("[BUSINESS] GenerateFixedPlan 用户已有活跃计划, plan_id=%s, 强制重新生成=%t",
			existingPlan.Id, req.ForceRegenerate)
	} else {
		logrus.Infof("[BUSINESS] GenerateFixedPlan 用户没有活跃计划")
	}

	// 如果用户已有活跃计划且不强制重新生成，则返回错误
	if existingPlan != nil && !req.ForceRegenerate {
		logrus.Warnf("[BUSINESS] GenerateFixedPlan 用户已有活跃计划且未设置强制重新生成, uid=%s", uid)
		return web.JsonParamErr("用户已有活跃计划，如需重新生成请设置forceRegenerate=true")
	}

	// 验证学习日期配置
	logrus.Infof("[BUSINESS] GenerateFixedPlan 验证学习日期配置...")
	if len(req.StudyDaysOfWeek) == 0 {
		logrus.Errorf("[BUSINESS] GenerateFixedPlan 学习日期配置为空")
		return web.JsonParamErr("请配置每周学习的具体日期")
	}

	// 验证学习日期是否在1-7范围内
	for _, day := range req.StudyDaysOfWeek {
		if day < 1 || day > 7 {
			logrus.Errorf("[BUSINESS] GenerateFixedPlan 学习日期验证失败, 无效日期=%d", day)
			return web.JsonParamErr("学习日期必须在1-7之间（1代表周一，7代表周日）")
		}
	}

	// 验证每天学习句数
	if req.DailySentences <= 0 {
		logrus.Errorf("[BUSINESS] GenerateFixedPlan 每天学习句数验证失败, sentences=%d", req.DailySentences)
		return web.JsonParamErr("每天学习句数必须大于0")
	}

	logrus.Infof("[BUSINESS] GenerateFixedPlan 所有参数验证通过, 学习日期=%v, 每日句数=%d, 总学习天数=%d",
		req.StudyDaysOfWeek, req.DailySentences, req.TotalLearnDays)

	// 获取第一个精选资源或第一个普通资源
	logrus.Infof("[BUSINESS] GenerateFixedPlan 获取第一个精选资源...")
	resource, err := r.model.GetFirstFeaturedResource(targetLangCode)
	if err != nil {
		logrus.WithError(err).Error("[BUSINESS] GenerateFixedPlan 获取资源失败 uid:", uid)
		return web.JsonInternalError(err)
	}
	logrus.Infof("[BUSINESS] GenerateFixedPlan 找到资源, resource_id=%s", resource.Id)

	// 如果存在活跃计划，立即将其状态设置为无效
	if existingPlan != nil {
		logrus.Infof("[BUSINESS] GenerateFixedPlan 开始使现有计划失效...")
		err = r.model.Tx(func(txDb *dbx.DBExtension) error {
			return txDb.Model(&model.LearningPlan{}).Where(model.LearningPlan{Uid: uid, Status: 1}).Update("status", 0).Error
		})

		if err != nil {
			logrus.WithError(err).Error("[BUSINESS] GenerateFixedPlan 使现有计划失效失败 uid:", uid)
			return web.JsonInternalError(err)
		}

		logrus.Infof("[BUSINESS] GenerateFixedPlan 用户 %s 的现有计划已设置为无效", uid)
	}

	// 计算学习天数和周数
	totalLearnDays := req.TotalLearnDays // 固定28天
	// 修复：应该根据总学习天数来计算需要的周数，而不是假设每周都学习所有配置的天数
	totalWeeks := (totalLearnDays + len(req.StudyDaysOfWeek) - 1) / len(req.StudyDaysOfWeek) // 向上取整

	// 使用事务处理创建计划
	err = r.model.Tx(func(txDb *dbx.DBExtension) error {
		// 1. 创建新计划
		now := timex.Now()
		endDate := now.AddDate(0, 0, totalLearnDays)

		plan := &model.LearningPlan{
			Uid:             uid,
			StartLevel:      req.CurrentLevel,
			TargetLevel:     req.TargetLevel,
			StartTimestamp:  timex.GetDateZeroTimestamp(now),
			EndTimestamp:    timex.GetDateZeroTimestamp(endDate),
			Status:          1,
			StudyDaysOfWeek: req.StudyDaysOfWeek,
			DailySentences:  req.DailySentences,
			TotalLearnDays:  totalLearnDays,
		}

		if err := txDb.Create(plan).Error; err != nil {
			return err
		}

		// 2. 创建阶段
		// 将资源ID序列化为JSON
		resourceIdsJSON, err := json.Marshal([]string{resource.Id})
		if err != nil {
			return err
		}

		stage := &model.LearningPlanStage{
			PlanId:      plan.Id,
			StageDesc:   "固定学习计划",
			Objective:   "通过固定学习计划提升英语水平",
			SortOrder:   1,
			Uid:         uid,
			ResourceIds: string(resourceIdsJSON),
		}

		if err := txDb.Create(stage).Error; err != nil {
			return err
		}

		// 3. 创建周计划和学习天
		remainingLearnDays := totalLearnDays // 剩余需要学习的天数
		weekNum := 1
		totalGeneratedDays := 0 // 记录总共生成的学习天数

		// 计算当前周的周一日期
		currentWeekday := int(now.Weekday())
		if currentWeekday == 0 { // Sunday = 0, 转换为 7
			currentWeekday = 7
		}
		daysToMonday := currentWeekday - 1
		currentWeekMonday := now.AddDate(0, 0, -daysToMonday)

		// 从当前周开始生成计划
		weekStartDate := currentWeekMonday

		for remainingLearnDays > 0 {
			weekEndDate := weekStartDate.AddDate(0, 0, 6)

			week := &model.LearningPlanWeek{
				StageId:        stage.Id,
				WeekNumber:     weekNum,
				StartTimestamp: timex.GetDateZeroTimestamp(weekStartDate),
				EndTimestamp:   timex.GetDateZeroTimestamp(weekEndDate),
				Uid:            uid,
				PlanId:         plan.Id,
			}

			if err := txDb.Create(week).Error; err != nil {
				return err
			}

			// 4. 为当前周创建学习天的计划
			var weekStudyDays []int
			var weekStudyDates []time.Time

			// 遍历配置的学习日，计算当前周的学习日期
			for _, dayOfWeek := range req.StudyDaysOfWeek {
				studyDate := calculateStudyDate(weekStartDate, dayOfWeek)

				// 对于第一周，只安排今天及之后的学习日
				// 对于后续周，安排所有配置的学习日
				if weekNum == 1 {
					// 第一周：只有当学习日期>=今天时，才安排学习
					todayDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
					if !studyDate.Before(todayDate) {
						weekStudyDays = append(weekStudyDays, dayOfWeek)
						weekStudyDates = append(weekStudyDates, studyDate)
					}
				} else {
					// 后续周：安排所有配置的学习日
					weekStudyDays = append(weekStudyDays, dayOfWeek)
					weekStudyDates = append(weekStudyDates, studyDate)
				}
			}

			// 计算当前周实际可以学习的天数
			daysThisWeek := int(math.Min(float64(remainingLearnDays), float64(len(weekStudyDays))))

			// 创建当前周的学习天
			for i := 0; i < daysThisWeek; i++ {
				studyDate := weekStudyDates[i]

				// 将资源ID序列化为JSON
				resourceIdsJSON, err := json.Marshal([]string{resource.Id})
				if err != nil {
					return err
				}

				day := &model.LearningPlanDay{
					WeekId:           week.Id,
					StageId:          stage.Id,
					PlanId:           plan.Id,
					Uid:              uid,
					PlanDayNumber:    totalGeneratedDays + 1,                // 使用整个计划中的学习天顺序（从1开始）
					WeekDayNumber:    i + 1,                                 // 在当前周中的学习天顺序
					StudyTimestamp:   timex.GetDateZeroTimestamp(studyDate), // 实际学习日期
					Status:           0,                                     // 初始状态：未开始
					CurrentSentences: 0,                                     // 初始已学习句数
					TargetSentences:  req.DailySentences,                    // 目标学习句数
					ResourceIds:      string(resourceIdsJSON),
				}

				if err := txDb.Create(day).Error; err != nil {
					return err
				}

				totalGeneratedDays++
			}

			// 更新剩余学习天数
			remainingLearnDays -= daysThisWeek

			// 移动到下一周的周一
			weekStartDate = weekStartDate.AddDate(0, 0, 7)
			weekNum++
		}

		return nil
	})

	if err != nil {
		logrus.WithError(err).Error("创建固定计划失败 uid:", uid)
		return web.JsonInternalError(err)
	}

	logrus.Infof("用户 %s 的固定学习计划生成完成，总天数: %d，总周数: %d", uid, totalLearnDays, totalWeeks)

	return web.JsonData(map[string]any{
		"message":        fmt.Sprintf("固定学习计划生成完成，总天数: %d，总周数: %d", totalLearnDays, totalWeeks),
		"status":         "completed",
		"totalLearnDays": totalLearnDays,
		"totalWeeks":     totalWeeks,
	})
}

// 计算每周的学习时长（秒）
func calculateWeeklyStudyTime(dailyStudyMinutes int) int {
	return dailyStudyMinutes * 60 * 7 // 每天学习分钟数 * 60秒 * 7天
}

// generateStageResources 生成阶段资源排序
func (r *PlanRepo) generateStageResources(ctx context.Context, questionnaire *model.UserQuestionnaire, resourceInfos []map[string]any) (*types.StageResourceListResponse, error) {
	// 生成阶段时长映射
	stageDurations := util.GenerateStageDurations(questionnaire.CurrentLevel, questionnaire.TargetLevel)

	// 构建阶段资源排序的提示信息
	prompt := util.BuildStageResourcesPrompt(questionnaire, resourceInfos, stageDurations)
	logrus.Infof("阶段资源排序 AI Prompt: %s", prompt)

	// 从环境变量或配置中获取API密钥
	apiKey := os.Getenv("ARK_API_KEY")

	// 调用AI服务
	content, err := util.CallAIService(ctx, prompt, apiKey, "deepseek-v3-250324")
	if err != nil {
		logrus.WithError(err).Error("调用AI服务生成阶段资源排序失败")
		return nil, err
	}

	// 预处理内容，提取JSON
	jsonContent, err := util.ExtractJSONFromContent(content)
	if err != nil {
		logrus.WithError(err).Error("提取JSON内容失败")
		return nil, err
	}

	// 解析AI返回的JSON字符串为StageResourcesSortResponse结构
	var sortResponse types.StageResourcesSortResponse
	if err := json.Unmarshal([]byte(jsonContent), &sortResponse); err != nil {
		logrus.WithError(err).Error("解析AI生成的阶段资源排序失败")
		return nil, err
	}

	// 验证响应数据的有效性
	if len(sortResponse.Stages) == 0 {
		logrus.Error("AI生成的阶段资源排序为空")
		return nil, errors.New("AI生成的阶段资源排序为空")
	}

	// 创建资源ID到序号的映射
	resourceIdToNumber := make(map[string]int)
	for i, info := range resourceInfos {
		if id, ok := info["id"].(string); ok {
			resourceIdToNumber[id] = i + 1 // 从1开始的序号
		}
	}

	// 创建序号到资源ID的映射
	numberToResourceId := make(map[int]string)
	for id, number := range resourceIdToNumber {
		numberToResourceId[number] = id
	}

	// 转换为StageResourceListResponse格式
	response := &types.StageResourceListResponse{
		Stages: make([]types.StageResourceList, len(sortResponse.Stages)),
	}

	for i, stage := range sortResponse.Stages {
		// 创建阶段响应
		stageResponse := types.StageResourceList{
			BaseStageResponse: stage.BaseStageResponse,
			ResourceIds:       make([]string, 0, len(stage.ResourceNumbers)),
		}

		// 将资源序号转换为资源ID
		for _, number := range stage.ResourceNumbers {
			if resourceId, ok := numberToResourceId[number]; ok {
				stageResponse.ResourceIds = append(stageResponse.ResourceIds, resourceId)
			}
		}

		response.Stages[i] = stageResponse
	}

	return response, nil
}

// callGenerateStagedPlanAIService 调用AI服务分阶段生成计划
func (r *PlanRepo) callGenerateStagedPlanAIService(ctx context.Context, questionnaire *model.UserQuestionnaire, resourceInfos []map[string]any) (*types.PlanAIResponse, error) {
	// 1. 首先调用AI服务生成阶段资源列表
	logrus.Info("开始生成阶段资源列表...")
	stageResources, err := r.generateStageResources(ctx, questionnaire, resourceInfos)
	if err != nil {
		logrus.WithError(err).Error("生成阶段资源列表失败")
		return nil, err
	}

	// 2. 创建资源使用状态映射
	resourceUsageMap := make(map[string]*types.ResourceUsage)
	for _, info := range resourceInfos {
		if id, ok := info["id"].(string); ok {
			if duration, ok := info["duration"].(int); ok {
				resourceUsageMap[id] = &types.ResourceUsage{
					ResourceId: id,
					Duration:   duration,
				}
			}
		}
	}

	// 3. 计算每周学习时长
	weeklyStudyTime := calculateWeeklyStudyTime(questionnaire.DailyStudyMinutes)
	logrus.Infof("每周学习时长: %d秒", weeklyStudyTime)

	// 生成阶段时长映射
	stageDurations := util.GenerateStageDurations(questionnaire.CurrentLevel, questionnaire.TargetLevel)

	// 4. 生成阶段计划
	finalResponse := &types.PlanAIResponse{
		Stages: make([]types.PlanStageAIResponse, len(stageResources.Stages)),
	}

	// 5. 处理每个阶段
	for i, stage := range stageResources.Stages {
		// 创建阶段响应
		stageResponse := types.PlanStageAIResponse{
			BaseStageResponse: stage.BaseStageResponse,
			Weeks:             make([]types.PlanWeekAIResponse, 0),
		}

		// 获取当前阶段的时长（秒）
		stageDurationStr, ok := stageDurations[stage.StageDesc]
		if !ok {
			logrus.Warnf("未找到阶段 %s 的时长信息，使用默认值", stage.StageDesc)
			stageDurationStr = "60-90h" // 使用默认值
		}

		// 将时长字符串转换为秒数
		minSeconds, maxSeconds := util.ConvertDurationToSeconds(stageDurationStr)
		// 使用平均时长作为阶段时长
		stageDuration := (minSeconds + maxSeconds) / 2
		logrus.Infof("阶段 %s 目标时长: %d-%d秒 (平均: %d秒)", stage.StageDesc, minSeconds, maxSeconds, stageDuration)

		// 计算阶段需要的总周数（向上取整）
		totalWeeks := (stageDuration + weeklyStudyTime - 1) / weeklyStudyTime
		if totalWeeks < 1 {
			totalWeeks = 1 // 确保至少有一周
		}
		logrus.Infof("阶段 %s 需要 %d 周完成", stage.StageDesc, totalWeeks)

		// 计算每周应该完成的学习时长
		weeklyTargetDuration := stageDuration / totalWeeks
		if weeklyTargetDuration < 1 {
			weeklyTargetDuration = 1 // 确保每周至少有一个单位的学习时长
		}

		// 初始化所有周
		for weekNum := 1; weekNum <= totalWeeks; weekNum++ {
			stageResponse.Weeks = append(stageResponse.Weeks, types.PlanWeekAIResponse{
				WeekNumber: weekNum,
				Resources:  make([]types.PlanResourceAIResponse, 0),
			})
		}

		// 计算每个资源应该分配到的周数
		resourceCount := len(stage.ResourceIds)
		resourcesPerWeek := (resourceCount + totalWeeks - 1) / totalWeeks // 向上取整

		// 当前处理的周索引和资源计数
		currentWeekIndex := 0
		currentWeekResourceCount := 0
		currentWeekDuration := 0

		// 处理每个资源
		for _, resourceId := range stage.ResourceIds {
			// 获取资源信息
			resourceUsage, ok := resourceUsageMap[resourceId]
			if !ok {
				continue
			}

			// 如果当前周的资源数量达到目标，或者当前周时长接近目标，移动到下一周
			if (currentWeekResourceCount >= resourcesPerWeek ||
				currentWeekDuration >= weeklyTargetDuration) &&
				currentWeekIndex < totalWeeks-1 {
				currentWeekIndex++
				currentWeekResourceCount = 0
				currentWeekDuration = 0
			}

			// 计算在当前周可以学习的次数
			remainingWeekTime := weeklyStudyTime - currentWeekDuration
			lsCount := 1 // 默认至少学习一次

			if resourceUsage.Duration > 0 {
				// 计算在当前周剩余时间内可以学习的次数
				possibleCount := remainingWeekTime / resourceUsage.Duration
				if possibleCount > 0 {
					lsCount = possibleCount
				}
			}

			// 如果当前周剩余时间不足，且还有下一周，则减少学习次数
			if currentWeekDuration+(resourceUsage.Duration*lsCount) > weeklyStudyTime && currentWeekIndex < totalWeeks-1 {
				lsCount = (weeklyStudyTime - currentWeekDuration) / resourceUsage.Duration
				if lsCount < 1 {
					lsCount = 1
				}
			}

			// 添加资源到当前周
			stageResponse.Weeks[currentWeekIndex].Resources = append(
				stageResponse.Weeks[currentWeekIndex].Resources,
				types.PlanResourceAIResponse{
					ResourceId: resourceId,
					LsCount:    lsCount,
				},
			)

			// 更新当前周已用时间和资源计数
			currentWeekDuration += resourceUsage.Duration * lsCount
			currentWeekResourceCount++
		}

		// 移除空的周
		validWeeks := make([]types.PlanWeekAIResponse, 0, len(stageResponse.Weeks))
		for _, week := range stageResponse.Weeks {
			if len(week.Resources) > 0 {
				validWeeks = append(validWeeks, week)
			}
		}
		stageResponse.Weeks = validWeeks

		// 如果没有分配任何资源到周，添加一个空的第一周
		if len(stageResponse.Weeks) == 0 {
			stageResponse.Weeks = append(stageResponse.Weeks, types.PlanWeekAIResponse{
				WeekNumber: 1,
				Resources:  make([]types.PlanResourceAIResponse, 0),
			})
		}

		finalResponse.Stages[i] = stageResponse
	}

	// 6. 计算每个阶段的开始和结束时间
	finalResponse.Stages = util.CalculatePlanStagesTime(finalResponse.Stages)

	return finalResponse, nil
}

// callFilterResourcesAIService 调用AI服务筛选资源
func (r *PlanRepo) callFilterResourcesAIService(ctx context.Context, questionnaire *model.UserQuestionnaire, resources []*model.Resource) ([]*model.Resource, error) {
	// 获取配置的每批最大资源数量
	maxResourcesPerBatch := r.config.AI.MaxResourcesPerBatch
	if maxResourcesPerBatch <= 0 {
		// 如果配置无效，使用默认值50
		maxResourcesPerBatch = 50
	}

	totalResources := len(resources)
	logrus.Infof("开始筛选资源，总资源数量: %d，每批处理数量: %d", totalResources, maxResourcesPerBatch)

	// 如果资源数量小于等于每批最大数量，直接处理
	if totalResources <= maxResourcesPerBatch {
		return r.filterResourcesBatch(ctx, questionnaire, resources)
	}

	// 计算需要处理的批次数
	batchCount := (totalResources + maxResourcesPerBatch - 1) / maxResourcesPerBatch
	logrus.Infof("将分成 %d 个批次并发处理", batchCount)

	// 创建通道用于接收每个批次的处理结果
	type batchResult struct {
		resources []*model.Resource
		err       error
		batchNum  int
	}
	resultChan := make(chan batchResult, batchCount)

	// 创建一个WaitGroup来等待所有goroutine完成
	var wg sync.WaitGroup

	// 并发处理每个批次
	for i := 0; i < batchCount; i++ {
		wg.Add(1)
		go func(batchIndex int) {
			defer wg.Done()

			// 计算当前批次的起始和结束索引
			start := batchIndex * maxResourcesPerBatch
			end := min((batchIndex+1)*maxResourcesPerBatch, totalResources)

			// 获取当前批次的资源
			batchResources := resources[start:end]
			logrus.Infof("开始处理第 %d/%d 批资源，数量: %d", batchIndex+1, batchCount, len(batchResources))

			// 处理当前批次
			filteredResources, err := r.filterResourcesBatch(ctx, questionnaire, batchResources)

			// 将结果发送到通道
			resultChan <- batchResult{
				resources: filteredResources,
				err:       err,
				batchNum:  batchIndex + 1,
			}

			if err != nil {
				logrus.WithError(err).Errorf("处理第 %d 批资源失败", batchIndex+1)
			} else {
				logrus.Infof("第 %d 批资源处理完成，筛选出 %d 个资源", batchIndex+1, len(filteredResources))
			}
		}(i)
	}

	// 启动一个goroutine来关闭结果通道
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集所有批次的结果
	var allFilteredResources []*model.Resource
	var allResourceIds []string
	successCount := 0

	// 使用互斥锁保护共享资源
	var mu sync.Mutex

	for result := range resultChan {
		if result.err != nil {
			// 如果处理失败，记录错误但继续处理其他批次的结果
			continue
		}

		mu.Lock()
		successCount++
		// 收集当前批次筛选出的资源
		for _, resource := range result.resources {
			// 避免重复添加
			if !contains(allResourceIds, resource.Id) {
				allResourceIds = append(allResourceIds, resource.Id)
				allFilteredResources = append(allFilteredResources, resource)
			}
		}
		mu.Unlock()
	}

	// 如果所有批次都处理失败，返回错误
	if successCount == 0 {
		logrus.Error("所有批次处理均失败")
		return nil, errors.New("所有批次处理均失败")
	}

	// 如果所有批次处理后没有筛选出任何资源，返回错误
	if len(allFilteredResources) == 0 {
		logrus.Error("所有批次处理后，AI筛选的资源为空")
		return nil, errors.New("AI筛选的资源为空")
	}

	// 记录成功的响应
	logrus.Infof("所有批次处理完成，成功处理 %d/%d 批次，筛选出 %d 个资源",
		successCount, batchCount, len(allFilteredResources))

	return allFilteredResources, nil
}

// filterResourcesBatch 处理单批资源
func (r *PlanRepo) filterResourcesBatch(ctx context.Context, questionnaire *model.UserQuestionnaire, resources []*model.Resource) ([]*model.Resource, error) {
	// 准备资源信息，包括ID和标签
	resourceInfos := util.PrepareResourceInfos(resources)

	// 构建筛选资源的提示信息
	prompt := util.BuildFilterResourcesPrompt(questionnaire, resourceInfos)
	logrus.Infof("筛选资源批次 AI Prompt: %s", prompt)

	// 从环境变量或配置中获取API密钥
	apiKey := os.Getenv("ARK_API_KEY")

	// 调用AI服务
	content, err := util.CallAIService(ctx, prompt, apiKey, "deepseek-v3-250324")
	if err != nil {
		logrus.WithError(err).Error("调用AI服务筛选资源失败")
		return nil, err
	}

	// 解析AI返回的JSON字符串为资源ID列表
	type FilteredResourcesResponse struct {
		ResourceIds []string `json:"resourceIds"`
	}

	var filteredResourcesResponse FilteredResourcesResponse
	if err := json.Unmarshal([]byte(content), &filteredResourcesResponse); err != nil {
		logrus.WithError(err).Error("解析AI筛选的资源响应失败")
		return nil, err
	}

	// 验证响应数据的有效性
	if len(filteredResourcesResponse.ResourceIds) == 0 {
		logrus.Error("AI筛选的资源ID为空")
		return nil, errors.New("AI筛选的资源ID为空")
	}

	// 根据筛选后的资源ID获取资源
	var filteredResources []*model.Resource
	resourceMap := make(map[string]*model.Resource)

	// 创建资源ID到资源的映射
	for _, resource := range resources {
		resourceMap[resource.Id] = resource
	}

	// 根据筛选后的资源ID获取资源
	for _, resourceId := range filteredResourcesResponse.ResourceIds {
		if resource, ok := resourceMap[resourceId]; ok {
			filteredResources = append(filteredResources, resource)
		}
	}

	// 记录成功的响应
	logrus.Infof("批次筛选后的资源数量: %d", len(filteredResources))

	return filteredResources, nil
}

// checkAndUpdatePlanDates 检查并自动调整计划中过期的学习天日期
// 这是核心的日期检查逻辑，在获取计划时自动调用
func (r *PlanRepo) checkAndUpdatePlanDates(plan *model.LearningPlan) error {
	now := timex.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// 获取所有学习天，按PlanDayNumber排序
	allDays, err := r.model.GetPlanAllDays(plan.Id)
	if err != nil {
		return fmt.Errorf("获取计划学习天失败: %w", err)
	}

	if len(allDays) == 0 {
		return nil // 没有学习天，无需检查
	}

	// 排序确保按学习天顺序处理
	sort.Slice(allDays, func(i, j int) bool {
		return allDays[i].PlanDayNumber < allDays[j].PlanDayNumber
	})

	// 检查是否有需要调整的情况
	// 只有未开始的天(status=0)才能顺延到下一天
	// status=1(进行中)和status=2(已完成)都属于历史数据，不能改动
	needsUpdate := false
	for _, day := range allDays {
		// 只有未开始的天(status=0)且已过期才需要调整
		if day.Status == int(enum.PlanDayStatusNotStarted) { // 只有未开始的天
			dayDate := timex.TimestampToTime(day.StudyTimestamp)
			if dayDate.Before(today) { // 学习日期已过期
				needsUpdate = true
				break
			}
		}
		// status=1(进行中)和status=2(已完成)的天不进行调整，保持历史数据
	}

	// 如果需要更新，调用优化后的重新计算日期的方法，传入已获取的数据
	if needsUpdate {
		logrus.Infof("检测到计划 %s 有过期的未开始学习天，开始自动调整日期", plan.Id)
		err = r.model.UpdatePlanDaysDateFromCurrentWithPlanData(plan, allDays)
		if err != nil {
			return fmt.Errorf("更新计划日期失败: %w", err)
		}
		logrus.Infof("计划 %s 日期调整完成", plan.Id)
	} else {
		logrus.Infof("计划 %s 无需调整", plan.Id)
	}

	return nil
}

// contains 检查字符串切片是否包含指定字符串
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// calculateStudyDate 根据周开始日期和学习日计算实际学习日期
func calculateStudyDate(weekStartDate time.Time, dayOfWeek int) time.Time {
	// 计算从周开始日期到目标学习日的天数
	// dayOfWeek: 1=周一, 2=周二, ..., 7=周日
	daysToAdd := dayOfWeek - 1 // 如果dayOfWeek=1(周一)，则不需要加天数

	// 从周开始日期开始计算，并确保只保留年月日
	resultDate := weekStartDate.AddDate(0, 0, daysToAdd)
	return time.Date(resultDate.Year(), resultDate.Month(), resultDate.Day(), 0, 0, 0, 0, resultDate.Location())
}

// UpdateSentences 更新学习句数
func (r *PlanRepo) UpdateSentences(c *gin.Context, req request.UpdateSentencesReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	updatedRanges, err := r.model.UpdateDayRecordedRanges(req.DayId, req.RecordedRange)
	if err != nil {
		logrus.WithError(err).Error("更新学习句数失败 uid:", uid)
		return web.JsonInternalError(err)
	}

	// 返回更新后的录音句子时间范围数组
	return web.JsonData(updatedRanges)
}

// UpdateStudyDays 修改学习计划的学习日期配置
// 优化版本：统一处理学习日期配置更新和日程重新安排
func (r *PlanRepo) UpdateStudyDays(c *gin.Context, req request.UpdateStudyDaysReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	logrus.Infof("[BUSINESS] UpdateStudyDays 开始处理, uid=%s, 请求学习日期=%v", uid, req.StudyDaysOfWeek)

	// 验证学习日期配置
	logrus.Infof("[BUSINESS] UpdateStudyDays 验证学习日期配置中...")
	if err := r.model.ValidateStudyDaysOfWeek(req.StudyDaysOfWeek); err != nil {
		logrus.Errorf("[BUSINESS] UpdateStudyDays 学习日期配置验证失败: %v", err)
		return web.JsonParamErr(err.Error())
	}

	// 获取用户活跃计划
	logrus.Infof("[BUSINESS] UpdateStudyDays 获取用户活跃计划中...")
	plan, err := r.model.GetActiveLearningPlan(uid)
	if err != nil {
		logrus.WithError(err).Error("[BUSINESS] UpdateStudyDays 获取活跃计划失败 uid:", uid)
		return web.JsonInternalError(err)
	}
	if plan == nil {
		logrus.Warnf("[BUSINESS] UpdateStudyDays 用户没有活跃计划, uid=%s", uid)
		return web.JsonEntityNotFound(c)
	}

	// 记录操作日志
	logrus.Infof("[BUSINESS] UpdateStudyDays 找到活跃计划, plan_id=%s, 当前学习日期=%v, 请求修改为=%v",
		plan.Id, plan.StudyDaysOfWeek, req.StudyDaysOfWeek)

	// 如果配置没有变化，直接返回成功
	if funk.IsEqual(plan.StudyDaysOfWeek, req.StudyDaysOfWeek) {
		logrus.Infof("[BUSINESS] UpdateStudyDays 学习日期配置未发生变化，直接返回成功, uid=%s", uid)
		return web.JsonOK()
	}

	// 调用优化后的统一方法：更新配置并重新安排日程
	logrus.Infof("[BUSINESS] UpdateStudyDays 开始更新配置并重新安排日程...")
	err = r.model.UpdateStudyDaysAndReschedule(plan.Id, req.StudyDaysOfWeek)
	if err != nil {
		logrus.WithError(err).Error("[BUSINESS] UpdateStudyDays 修改学习日期并重新安排日程失败 uid:", uid)
		return web.JsonInternalError(err)
	}

	logrus.Infof("[BUSINESS] UpdateStudyDays 学习日期配置修改成功, uid=%s", uid)
	return web.JsonOK()
}

// UpdateDailySentences 修改学习计划的每日句数
func (r *PlanRepo) UpdateDailySentences(c *gin.Context, req request.UpdateDailySentencesReq) *web.JsonResult {
	uid := jwtx.GetUid(c)

	// 验证每日句数
	if req.DailySentences <= 0 {
		return web.JsonParamErr("每日句数必须大于0")
	}

	// 获取用户活跃计划
	plan, err := r.model.GetActiveLearningPlan(uid)
	if err != nil {
		logrus.WithError(err).Error("获取活跃计划失败 uid:", uid)
		return web.JsonInternalError(err)
	}
	if plan == nil {
		return web.JsonEntityNotFound(c)
	}

	// 记录操作日志
	logrus.Infof("用户 %s 请求修改每日句数，从 %d 改为 %d", uid, plan.DailySentences, req.DailySentences)

	// 如果句数没有变化，直接返回成功
	if plan.DailySentences == req.DailySentences {
		logrus.Infof("用户 %s 的每日句数未发生变化，直接返回成功", uid)
		return web.JsonOK()
	}

	// 调用模型层方法更新每日句数
	err = r.model.UpdateLearningPlanDailySentences(plan.Id, req.DailySentences)
	if err != nil {
		logrus.WithError(err).Error("修改每日句数失败 uid:", uid)
		return web.JsonInternalError(err)
	}

	// 修改每日句数后，可能影响学习进度，检查并更新日期
	err = r.checkAndUpdatePlanDates(plan)
	if err != nil {
		logrus.WithError(err).Warn("重新检查学习日期失败，但句数修改成功 uid:", uid)
	}

	logrus.Infof("用户 %s 的每日句数修改成功", uid)
	return web.JsonOK()
}

// UpdatePlanResource 修改学习计划的资源
func (r *PlanRepo) UpdatePlanResource(c *gin.Context, req request.UpdatePlanResourceReq) *web.JsonResult {
	uid := jwtx.GetUid(c)

	// 获取用户活跃计划
	plan, err := r.model.GetActiveLearningPlan(uid)
	if err != nil {
		logrus.WithError(err).Error("获取活跃计划失败 uid:", uid)
		return web.JsonInternalError(err)
	}
	if plan == nil {
		return web.JsonEntityNotFound(c)
	}

	// 记录操作日志
	logrus.Infof("用户 %s 请求修改学习计划资源，计划ID: %s，新资源ID: %s", uid, plan.Id, req.ResourceId)

	// 调用模型层方法更新计划资源
	err = r.model.UpdateLearningPlanResource(plan.Id, req.ResourceId)
	if err != nil {
		logrus.WithError(err).Error("修改学习计划资源失败 uid:", uid, "planId:", plan.Id, "resourceId:", req.ResourceId)
		return web.JsonInternalError(err)
	}

	logrus.Infof("用户 %s 的学习计划资源修改成功，计划ID: %s，新资源ID: %s", uid, plan.Id, req.ResourceId)
	return web.JsonOK()
}

// GetDayRecordedRanges 根据学习天ID获取录音句子时间范围数组
func (r *PlanRepo) GetDay(c *gin.Context, req request.IdReq) *web.JsonResult {
	day, err := r.model.GetDayRecordedRanges(req.Id)
	if err != nil {
		logrus.WithError(err).Error("获取学习天录音句子时间范围失败 dayId:", req.Id)
		return web.JsonInternalError(err)
	}
	dayResp := &response.PlanDayResp{}
	copier.Copy(dayResp, day)
	return web.JsonData(dayResp)
}
