package data

import (
	"loop/internal/config"
	"loop/internal/model"
	"loop/internal/request"
	"loop/internal/response"
	"loop/pkg/web"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
)

func NewFeedbackRepo(feedbackModel *model.FeedbackModel, userModel *model.UserModel, config *config.Config) *FeedbackRepo {
	return &FeedbackRepo{
		feedbackModel: feedbackModel,
		userModel:     userModel,
		config:        config,
	}
}

type FeedbackRepo struct {
	feedbackModel *model.FeedbackModel
	userModel     *model.UserModel
	config        *config.Config
}

// CreateFeedback 创建用户反馈
func (f *FeedbackRepo) CreateFeedback(c *gin.Context, req request.CreateFeedbackReq) *web.JsonResult {
	uid := c.GetString("uid")
	if uid == "" {
		return web.JsonErrorCodeMsg(http.StatusUnauthorized, "用户未登录")
	}

	feedback := &model.UserFeedback{
		Uid:         uid,
		Content:     req.Content,
		LogOssUrl:   req.LogOssUrl,
		ContactInfo: req.ContactInfo,
		DeviceInfo:  req.DeviceInfo,
		AppVersion:  req.AppVersion,
		Status:      0, // 默认待处理
	}

	if err := f.feedbackModel.CreateFeedback(feedback); err != nil {
		return web.JsonError("创建反馈失败: " + err.Error())
	}

	return web.JsonOK()
}

// GetUserFeedbacks 获取用户的反馈列表
func (f *FeedbackRepo) GetUserFeedbacks(c *gin.Context) *web.JsonResult {
	uid := c.GetString("uid")
	if uid == "" {
		return web.JsonErrorCodeMsg(http.StatusUnauthorized, "用户未登录")
	}

	feedbacks, err := f.feedbackModel.GetByUid(uid)
	if err != nil {
		return web.JsonError("获取反馈列表失败: " + err.Error())
	}

	var respList []response.UserFeedbackResp
	if err := copier.Copy(&respList, &feedbacks); err != nil {
		return web.JsonError("数据转换失败: " + err.Error())
	}

	return web.JsonData(respList)
}

// GetFeedbackList 获取反馈列表（管理员）
func (f *FeedbackRepo) GetFeedbackList(c *gin.Context, req request.FeedbackListReq) *web.JsonResult {
	if req.CurrentPage <= 0 {
		req.CurrentPage = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	feedbacks, total, err := f.feedbackModel.GetFeedbackList(req.CurrentPage, req.PageSize, req.Status)
	if err != nil {
		return web.JsonError("获取反馈列表失败: " + err.Error())
	}

	// 获取用户信息
	var respList []response.FeedbackListResp
	for _, feedback := range feedbacks {
		var resp response.FeedbackListResp
		if err := copier.Copy(&resp, &feedback); err != nil {
			continue
		}

		respList = append(respList, resp)
	}

	return web.JsonData(web.PageJsonResult{
		Data:  respList,
		Total: total,
	})

}

// UpdateFeedbackStatus 更新反馈状态
func (f *FeedbackRepo) UpdateFeedbackStatus(c *gin.Context, req request.UpdateFeedbackStatusReq) *web.JsonResult {
	if err := f.feedbackModel.UpdateStatus(req.Id, req.Status, req.AdminReply); err != nil {
		return web.JsonError("更新反馈状态失败: " + err.Error())
	}

	return web.JsonOK()
}

// GetFeedbackById 根据ID获取反馈详情
func (f *FeedbackRepo) GetFeedbackById(c *gin.Context, id string) *web.JsonResult {
	feedback, err := f.feedbackModel.GetById(id)
	if err != nil {
		return web.JsonError("获取反馈详情失败: " + err.Error())
	}
	if feedback == nil {
		return web.JsonError("反馈不存在")
	}

	var resp response.FeedbackDetailResp
	if err := copier.Copy(&resp, feedback); err != nil {
		return web.JsonError("数据转换失败: " + err.Error())
	}

	return web.JsonData(resp)
}
