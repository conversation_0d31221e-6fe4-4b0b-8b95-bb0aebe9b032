package request

import (
	"loop/pkg/types"
	"mime/multipart"
)

type UserLoginReq struct {
	Username string `form:"username" json:"username" binding:"required,min=5,max=30"`
	Password string `form:"password" json:"password" binding:"required,min=8,max=40"`
}
type UserRegisterReq struct {
	Nickname string `form:"nickname" json:"nickname" binding:"required,min=2,max=30"`
	Username string `form:"username" json:"username" binding:"required,min=5,max=30"`
	Password string `form:"password" json:"password" binding:"required,min=8,max=40"`
}
type UserAdminLoginReq struct {
	Username string `form:"username" json:"username" binding:"required"`
	Password string `form:"password" json:"password" binding:"required"`
}
type AppleLoginReq struct {
	UserIdentifier string `form:"userIdentifier" json:"userIdentifier" binding:"required"`
	IdentityToken  string `form:"identityToken" json:"identityToken" binding:"required"`
}

type UpdateUserInfoReq struct {
	Id             string `form:"id" json:"id"`
	Nickname       string `form:"nickname" json:"nickname"`
	Username       string `form:"username" json:"username"`
	Avatar         string `form:"avatar" json:"avatar"`
	Status         int    `form:"status" json:"status"`
	NativeLangCode string `form:"nativeLangCode" json:"nativeLangCode"`
	TargetLangCode string `form:"targetLangCode" json:"targetLangCode"`
}

type UploadFileReq struct {
	Files []*multipart.FileHeader `form:"files" binding:"required"`
}

type UpdateUserPlayerConfigRequest struct {
	SubtitleFontSize              *int            `json:"subtitleFontSize,omitempty"`
	SingleRepeatCount             *int            `json:"singleRepeatCount,omitempty"`
	ShowSubtitleNum               *int            `json:"showSubtitleNum,omitempty"`
	SubtitleTextBottomHeightRatio *float32        `json:"subtitleTextBottomHeightRatio,omitempty"`
	ShowSubtitleWhenRecordEnd     *bool           `json:"showSubtitleWhenRecordEnd,omitempty"`
	AutoPlayRecordWhenRecordEnd   *bool           `json:"autoPlayRecordWhenRecordEnd,omitempty"`
	AutoRecord                    *bool           `json:"autoRecord,omitempty"`
	AutoStopRecord                *bool           `json:"autoStopRecord,omitempty"`
	OpenSingleRepeat              *bool           `json:"openSingleRepeat,omitempty"`
	CoverSubtitle                 *bool           `json:"coverSubtitle,omitempty"`
	MenuSort                      *types.IntArray `json:"menuSort,omitempty"`
}

type WatchHistoryAddReq struct {
	//本地这里代表的就是 LocalResource 的id
	ResourceId string `form:"resourceId" json:"resourceId" binding:"required"`
	// 1代表远程的资源 2代表本地资源
	ResourceType int   `form:"resourceType" json:"resourceType" binding:"required"`
	Position     int64 `form:"position" json:"position" binding:"required"` // 用户停止观看的位置
}

type WatchHistoryDeleteReq struct {
	ResourceId   string `form:"resourceId" json:"resourceId" binding:"required"`
	ResourceType int    `form:"resourceType" json:"resourceType" binding:"required"`
}

// UpdateUserStorageReq 更新用户存储空间使用量的请求
type UpdateUserVideoLimitReq struct {
	Size int `form:"size" json:"size" binding:"required" example:"1024" minimum:"1"` // 消耗的存储空间大小(单位：字节)
}

// UpdateUserAICallLimitReq 更新AI调用次数限制的请求
type UpdateUserAICallLimitReq struct {
	Count int `form:"count" json:"count" binding:"required,min=1" example:"1" minimum:"1"` // 消耗的AI调用次数
}

// 注销账号请求
type DeleteAccountReq struct {
	Reason string `json:"reason" form:"reason" binding:"required" comment:"注销原因"`
}
