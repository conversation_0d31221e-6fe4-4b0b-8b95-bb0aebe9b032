package request

type EmptyReq struct {
}

type ListReq struct {
	CurrentPage int `form:"currentpage" json:"currentpage" `
	PageSize    int `form:"pagesize" json:"pagesize"`
	SortType    int `form:"sortType" json:"sortType"`
}

type IdReq struct {
	Id string `form:"id" json:"id" binding:"required"`
}
type PriorityReq struct {
	Id       string `form:"id" json:"id" binding:"required"`
	Priority int64  `form:"priority" json:"priority" binding:"required"`
}

type IdsReq struct {
	Id []string `form:"ids" json:"ids" binding:"required"`
}
type ResCategoryReq struct {
	Id             string `form:"id" json:"id"`
	Name           string `form:"name" json:"name" binding:"required"`
	Description    string `form:"description" json:"description"`
	CategoryTypeId string `form:"categoryTypeId" json:"categoryTypeId" binding:"required"`
}

type ResCategoryTypeReq struct {
	Id          string `form:"id" json:"id"`
	Name        string `form:"name" json:"name" binding:"required"`
	Description string `form:"description" json:"description"`
}

type UpdateResCategoryReq struct {
	Name        string `form:"name" json:"name" binding:"required"`
	Description string `form:"description" json:"description"`
}

type TedReq struct {
	Url string `form:"url" json:"url" binding:"required"`
}
