package request

type ResourceAddReq struct {
	Id                     string                `json:"id"`
	ResourceRelations      []ResourceRelationReq `json:"resourceRelations" binding:"required"`
	OriginResourceRelation ResourceRelationReq   `json:"originResourceRelation" binding:"required"`
	VideoURL               string                `json:"videoUrl" binding:"required"`
	Cover                  string                `json:"cover" binding:"required"`
	CategoryIds            []string              `json:"categoryIds"`
	SeriesIds              []string              `json:"seriesIds"`
	Duration               int64                 `json:"duration" binding:"required"`
	PublishedAt            string                `json:"publishedAt" binding:"required"`
	Author                 string                `json:"author" binding:"required"`
}

// TED 会有多个语言 多个标题、描述、字幕
type ResourceRelationReq struct {
	LangCode    string `form:"langCode" json:"langCode" binding:"required"`
	Title       string `json:"title" binding:"required"`
	Description string `json:"description" binding:"required"`
	SubtitleUrl string `json:"subtitleUrl" binding:"required"`
}

type ResourceListReq struct {
	CategoryID  string `form:"categoryId" json:"categoryId"`
	CurrentPage int    `form:"currentpage" json:"currentpage" binding:"required"`
	PageSize    int    `form:"pagesize" json:"pagesize" binding:"required"`
	SortType    int    `form:"sortType" json:"sortType"`
}

type ResourceNameModReq struct {
	ResourceId   string `form:"resourceId" json:"resourceId" binding:"required"`
	ResourceType int    `form:"resourceType" json:"resourceType" binding:"required"`
	Name         string `form:"name" json:"name" binding:"required"`
}

type ResourceHomeResourceReq struct {
	CategoryIds []string `form:"categoryIds" json:"categoryIds" binding:"required"`
}

// ResourceTagsReq 获取资源标签的请求
type ResourceTagsReq struct {
	ResourceId string `form:"resourceId" json:"resourceId" binding:"required"`
}

// SetResourceTagsReq 设置资源标签的请求
type SetResourceTagsReq struct {
	ResourceId string   `form:"resourceId" json:"resourceId" binding:"required"`
	Tags       []string `form:"tags" json:"tags" binding:"required"`
}

// ResourceTagReq 添加/移除资源标签的请求
type ResourceTagReq struct {
	ResourceId string `form:"resourceId" json:"resourceId" binding:"required"`
	Tag        string `form:"tag" json:"tag" binding:"required"`
}

// ResourcesByTagsReq 根据标签获取资源的请求
type ResourcesByTagsReq struct {
	Tags     []string `form:"tags" json:"tags" binding:"required"`
	LangCode string   `form:"langCode" json:"langCode"` // 可选，如果不提供则使用用户的目标语言
}

// GetAllResourcesReq 获取所有资源的请求
type GetAllResourcesReq struct {
	LangCode    string `form:"langCode" json:"langCode"`       // 语言代码，可选，如果不提供则使用请求头中的目标语言
	CurrentPage int    `form:"currentpage" json:"currentpage"` // 当前页码，从1开始
	PageSize    int    `form:"pagesize" json:"pagesize"`       // 每页数量
}

type ResourcesFeaturedContentReq struct {
	ResourceId string `form:"resourceId" json:"resourceId" binding:"required"`
	LangCode   string `form:"langCode" json:"langCode" binding:"required"`
}
