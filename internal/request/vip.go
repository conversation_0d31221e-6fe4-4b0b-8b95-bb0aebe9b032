package request

type CreatePayOrderReq struct {
	ProductId uint `form:"productId" json:"productId" binding:"required"` //商品ID
}

type ExchangeCodeReq struct {
	Uid  string `form:"uid" json:"uid"`
	Code string `form:"code" json:"code" binding:"required"`
}

type AddPromotionCodeReq struct {
	Days     int `form:"days" json:"days" binding:"required"`
	VipLevel int `form:"vipLevel" json:"vipLevel" binding:"required"`
}
