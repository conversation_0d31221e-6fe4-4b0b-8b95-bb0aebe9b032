package request

// 新的学习数据上传请求结构，匹配前端LearningDataUpload
type DataEpisodeEachReq struct {
	SessionData     LearningSessionReq `json:"sessionData" binding:"required"`
	SentenceRecords string             `json:"sentenceRecords"`
}

// 学习会话数据请求结构，匹配前端LearningSessionData
type LearningSessionReq struct {
	ResourceId           string `json:"resourceId" form:"resourceId" binding:"required"`
	ResourceType         int    `json:"resourceType" form:"resourceType" binding:"required"`
	SessionStartTime     int64  `json:"sessionStartTime" form:"sessionStartTime" binding:"required"`
	SessionEndTime       int64  `json:"sessionEndTime" form:"sessionEndTime" binding:"required"`
	TotalSessionDuration *int64 `json:"totalSessionDuration" form:"totalSessionDuration" binding:"required"`
	TotalPlayDuration    *int64 `json:"totalPlayDuration" form:"totalPlayDuration" binding:"required"`
	TotalRecordDuration  *int64 `json:"totalRecordDuration" form:"totalRecordDuration" binding:"required"`
	LsTimes              int64  `json:"lsTimes" form:"lsTimes"`
}

type DataEpisodeModLsTimeReq struct {
	//本地这里代表的就是 LocalResource 的id
	ResourceId string `form:"resourceId" json:"resourceId" binding:"required"`
	// 1代表远程的资源 2代表本地资源
	ResourceType   int   `form:"resourceType" json:"resourceType" binding:"required"`
	CurrentLsTimes int64 `form:"currentLsTimes" json:"currentLsTimes" binding:"required"`
}
type DataEpisodeTypeReq struct {
	// 1日 2周 3月 4年
	Type      int   `form:"type" json:"type" binding:"required"`
	StartTime int64 `form:"startTime" json:"startTime"`
	EndTime   int64 `form:"endTime" json:"endTime"`
}
type EpisodeLsDataReq struct {
	//本地这里代表的就是 LocalResource 的id
	ResourceId string `form:"resourceId" json:"resourceId" binding:"required"`
	// 1代表远程的资源 2代表本地资源
	ResourceType int `form:"resourceType" json:"resourceType" binding:"required"`
}
