package request

// CreateFeedbackReq 创建反馈请求
type CreateFeedbackReq struct {
	Content     string `json:"content" binding:"required" form:"content"`
	LogOssUrl   string `json:"logOssUrl" form:"logOssUrl"`
	ContactInfo string `json:"contactInfo" form:"contactInfo"`
	DeviceInfo  string `json:"deviceInfo" form:"deviceInfo"`
	AppVersion  string `json:"appVersion" form:"appVersion"`
}

// FeedbackListReq 反馈列表请求
type FeedbackListReq struct {
	ListReq
	Status *int `json:"status" form:"status"`
}

// UpdateFeedbackStatusReq 更新反馈状态请求
type UpdateFeedbackStatusReq struct {
	Id         string `json:"id" binding:"required" form:"id"`
	Status     int    `json:"status" binding:"required" form:"status"`
	AdminReply string `json:"adminReply" form:"adminReply"`
}
