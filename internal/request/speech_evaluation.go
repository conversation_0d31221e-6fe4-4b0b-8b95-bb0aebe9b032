package request

type SpeechEvaluationReq struct {
	ResourceId   string `json:"resourceId"`
	ResourceType int    `json:"resourceType"`
	Content      string `json:"content"`
	AudioUrl     string `json:"audioUrl"`
	StartTime    int64  `json:"startTime"`
	EndTime      int64  `json:"endTime"`
}

type SpeechEvaluationListReq struct {
	ResourceId   string `form:"resourceId" json:"resourceId"`
	ResourceType int    `form:"resourceType" json:"resourceType"`
}

type SpeechEvaluationUpdateReq struct {
	Id uint `json:"id"`
	SpeechEvaluationReq
}

// SpeechEvaluationBatchReq 批量处理语音评测请求
type SpeechEvaluationBatchReq struct {
	Evaluations []SpeechEvaluationReq `json:"evaluations" binding:"required"`
}

// SpeechEvaluationBatchUpdateReq 批量更新语音评测请求
type SpeechEvaluationBatchUpdateReq struct {
	Evaluations []SpeechEvaluationUpdateReq `json:"evaluations" binding:"required"`
}

// SpeechEvaluationBatchUpdateAudioUrlItem 用于批量更新AudioUrl的单项
// 只包含id和audioUrl
// SpeechEvaluationBatchUpdateAudioUrlReq 批量更新AudioUrl请求
// Evaluations为SpeechEvaluationBatchUpdateAudioUrlItem数组

type SpeechEvaluationBatchUpdateAudioUrlItem struct {
	Id       uint   `json:"id" binding:"required"`
	AudioUrl string `json:"audioUrl" binding:"required"`
}

type SpeechEvaluationBatchUpdateAudioUrlReq struct {
	Evaluations []SpeechEvaluationBatchUpdateAudioUrlItem `json:"evaluations" binding:"required"`
}
