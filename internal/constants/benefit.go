package constants

// BenefitType 权益类型
type BenefitCode string

const (
	// 权益组编码
	BenefitGroupVideoSizeLimit        BenefitCode = "VIDEO_SIZE_LIMIT"        // 单次视频大小限制，MB为单位
	BenefitGroupTotalVideoSizeLimit   BenefitCode = "TOTAL_VIDEO_SIZE_LIMIT"  // 总视频大小限制，MB为单位
	BenefitGroupAiCallsLimit          BenefitCode = "AI_CALLS_LIMIT"          // AI调用次数限制，次为单位
	BenefitGroupSubtitleDialogueLimit BenefitCode = "SUBTITLE_DIALOGUE_LIMIT" // 字幕对话次数限制，次为单位

	// 单次视频大小限制组权益
	BenefitCodeVideoSizeLimitBasic BenefitCode = "UPLOAD_LIMIT_BASIC" // 普通会员单次视频限制
	BenefitCodeVideoSizeLimitPro   BenefitCode = "UPLOAD_LIMIT_PRO"   // Pro会员单次视频限制
	BenefitCodeVideoSizeLimitUltra BenefitCode = "UPLOAD_LIMIT_ULTRA" // Ultra会员单次视频限制

	// 总视频大小限制组权益
	BenefitCodeTotalVideoSizeLimitBasic BenefitCode = "TOTAL_UPLOAD_LIMIT_BASIC" // 普通会员总视频限制
	BenefitCodeTotalVideoSizeLimitPro   BenefitCode = "TOTAL_UPLOAD_LIMIT_PRO"   // Pro会员总视频限制
	BenefitCodeTotalVideoSizeLimitUltra BenefitCode = "TOTAL_UPLOAD_LIMIT_ULTRA" // Ultra会员总视频限制

	// AI调用次数组权益
	BenefitCodeAICallsBasic BenefitCode = "AI_CALLS_BASIC" // 普通会员AI调用次数
	BenefitCodeAICallsPro   BenefitCode = "AI_CALLS_PRO"   // Pro会员AI调用次数
	BenefitCodeAICallsUltra BenefitCode = "AI_CALLS_ULTRA" // Ultra会员AI调用次数

	// 字幕对话次数组权益
	BenefitCodeSubtitleDialogueBasic BenefitCode = "SUBTITLE_DIALOGUE_BASIC" // 普通会员字幕对话次数
	BenefitCodeSubtitleDialoguePro   BenefitCode = "SUBTITLE_DIALOGUE_PRO"   // Pro会员字幕对话次数
	BenefitCodeSubtitleDialogueUltra BenefitCode = "SUBTITLE_DIALOGUE_ULTRA" // Ultra会员字幕对话次数
)
