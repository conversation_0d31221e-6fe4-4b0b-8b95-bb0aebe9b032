# VIP会员系统使用示例

## 重构说明

VIP系统已重构，**天数和会员等级完全分离**：
- **天数**：决定会员时长
- **等级**：决定会员权益

## 使用场景

### 1. 默认商品（固定等级）

```go
// 月卡：30天 + NORMAL等级
tradeProduct := TradeProduct{
    Name:         "月卡会员",
    Days:         30,
    VipID:        getVipIDByLevel(VIP_LEVEL_NORMAL),
    Price:        29.9,
    IosProductId: "com.yourapp.monthly",
}

// 年卡：365天 + ULTRA等级  
tradeProduct := TradeProduct{
    Name:         "年卡会员",
    Days:         365,
    VipID:        getVipIDByLevel(VIP_LEVEL_ULTITA),
    Price:        299.0,
    IosProductId: "com.yourapp.yearly",
}

// 半年卡：180天 + PRO等级
tradeProduct := TradeProduct{
    Name:         "半年卡会员", 
    Days:         180,
    VipID:        getVipIDByLevel(VIP_LEVEL_PRO),
    Price:        159.0,
    IosProductId: "com.yourapp.halfyear",
}
```

### 2. 兑换码（灵活配置）

```go
// 管理员可以灵活配置兑换码

// 7天试用（普通等级）
vipModel.AddPromotionCode(7, VIP_LEVEL_NORMAL)

// 30天PRO会员（短期高等级）
vipModel.AddPromotionCode(30, VIP_LEVEL_PRO)

// 90天PRO会员
vipModel.AddPromotionCode(90, VIP_LEVEL_PRO)

// 365天ULTRA会员
vipModel.AddPromotionCode(365, VIP_LEVEL_ULTITA)

// 7天ULTRA会员（短期体验高等级）
vipModel.AddPromotionCode(7, VIP_LEVEL_ULTITA)
```

### 3. API调用示例

```bash
# 创建兑换码
POST /api/v1/vip/promotion-code
{
    "days": 30,
    "vipLevel": 100  // PRO等级
}

# 兑换码使用
POST /api/v1/vip/exchange-code
{
    "code": "abc123"
}
```

## 等级定义

```go
const (
    VIP_LEVEL_NORMAL = 1    // 普通会员
    VIP_LEVEL_PRO    = 100  // PRO会员  
    VIP_LEVEL_ULTITA = 1000 // ULTRA会员
)
```

## 权益分配

每个等级对应不同的权益：

- **NORMAL (1)**：基础权益
- **PRO (100)**：高级权益  
- **ULTRA (1000)**：顶级权益

## 数据库结构

### PromotionCode表
```sql
CREATE TABLE `promotion_codes` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `status` int DEFAULT '0' COMMENT '0未兑换，1已兑换',
  `code` varchar(64) NOT NULL,
  `days` int DEFAULT '0' COMMENT '天数',
  `vip_level` int DEFAULT '1' COMMENT '会员等级', -- 新增字段
  `uid` varchar(64) COMMENT '兑换的用户ID',
  `redeemed_timestamp` bigint COMMENT '兑换时间',
  PRIMARY KEY (`id`)
);
```

## 测试用例

```go
// 测试不同组合
testCases := []struct {
    days     int
    vipLevel int
    desc     string
}{
    {7, VIP_LEVEL_NORMAL, "7天试用"},
    {30, VIP_LEVEL_PRO, "30天PRO会员"},
    {180, VIP_LEVEL_PRO, "半年PRO会员"},
    {365, VIP_LEVEL_ULTITA, "年卡ULTRA会员"},
    {7, VIP_LEVEL_ULTITA, "7天ULTRA体验"},
}
```

## 优势

1. **灵活运营**：可以创建任意天数和等级组合
2. **清晰逻辑**：天数和等级职责分离
3. **易于扩展**：新增等级不影响天数逻辑
4. **运营友好**：支持各种营销活动需求 