package timex

import (
	"time"
)

// 常用时间格式
const (
	// StandardFormat 标准时间格式 yyyy-MM-dd HH:mm:ss
	StandardFormat = "2006-01-02 15:04:05"
	// DateFormat 日期格式 yyyy-MM-dd
	DateFormat = "2006-01-02"
	// TimeFormat 时间格式 HH:mm:ss
	TimeFormat = "15:04:05"
	// RFC3339Format RFC3339格式
	RFC3339Format = time.RFC3339
)

// ParseStandard 解析标准时间格式 yyyy-MM-dd HH:mm:ss
func ParseStandard(timeStr string) (time.Time, error) {
	return time.Parse(StandardFormat, timeStr)
}

// FormatStandard 格式化为标准时间格式 yyyy-MM-dd HH:mm:ss
func FormatStandard(t time.Time) string {
	return t.Format(StandardFormat)
}

// ParseDate 解析日期格式 yyyy-MM-dd
func ParseDate(dateStr string) (time.Time, error) {
	return time.Parse(DateFormat, dateStr)
}

// FormatDate 格式化为日期格式 yyyy-MM-dd
func FormatDate(t time.Time) string {
	return t.Format(DateFormat)
}

// ParseRFC3339 解析RFC3339格式
func ParseRFC3339(timeStr string) (time.Time, error) {
	return time.Parse(RFC3339Format, timeStr)
}

// FormatRFC3339 格式化为RFC3339格式
func FormatRFC3339(t time.Time) string {
	return t.Format(RFC3339Format)
}

// Now 获取当前时间（中国时区）
// 如果设置了测试时间，则返回测试时间
func Now() time.Time {

	location, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return time.Now().AddDate(0, 0, 1) // 如果加载时区失败，返回本地时区的明天时间
	}
	return time.Now().In(location).AddDate(0, 0, 1)
	if err != nil {
		return time.Now() // 如果加载时区失败，返回本地时区的当前时间
	}
	return time.Now().In(location)

}

// NowString 获取当前时间的字符串表示（中国时区，标准格式）
func NowString() string {
	return FormatStandard(Now())
}

// AddMonths 添加指定月数
func AddMonths(t time.Time, months int) time.Time {
	return t.AddDate(0, months, 0)
}

// AddDays 添加指定天数
func AddDays(t time.Time, days int) time.Time {
	return t.AddDate(0, 0, days)
}

// IsZero 判断时间是否为零值
func IsZero(t time.Time) bool {
	return t.IsZero()
}

// DefaultIfZero 如果时间为零值，则返回默认时间
func DefaultIfZero(t time.Time, defaultTime time.Time) time.Time {
	if t.IsZero() {
		return defaultTime
	}
	return t
}

// DefaultNowIfZero 如果时间为零值，则返回当前时间
func DefaultNowIfZero(t time.Time) time.Time {
	return DefaultIfZero(t, Now())
}

// ParseWithFallback 尝试使用多种格式解析时间，如果都失败则返回默认时间
func ParseWithFallback(timeStr string, defaultTime time.Time) time.Time {
	// 尝试标准格式
	t, err := ParseStandard(timeStr)
	if err == nil {
		return t
	}

	// 尝试RFC3339格式
	t, err = ParseRFC3339(timeStr)
	if err == nil {
		return t
	}

	// 尝试日期格式
	t, err = ParseDate(timeStr)
	if err == nil {
		return t
	}

	// 都失败了，返回默认时间
	return defaultTime
}

// GetTodayZeroTimestamp 获取今天0点的时间戳（中国时区，毫秒级）
func GetTodayZeroTimestamp() int64 {
	now := Now()
	// 获取今天0点0分0秒的时间
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	return today.UnixMilli()
}

// GetDateZeroTimestamp 获取指定日期0点的时间戳（中国时区，毫秒级）
func GetDateZeroTimestamp(t time.Time) int64 {
	location, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		location = t.Location()
	}
	// 转换到中国时区
	chinaTime := t.In(location)
	// 获取该日期0点0分0秒的时间
	zeroTime := time.Date(chinaTime.Year(), chinaTime.Month(), chinaTime.Day(), 0, 0, 0, 0, location)
	return zeroTime.UnixMilli()
}

// TimestampToTime 将时间戳转换为时间对象（中国时区，毫秒级）
func TimestampToTime(timestamp int64) time.Time {
	location, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return time.UnixMilli(timestamp)
	}
	return time.UnixMilli(timestamp).In(location)
}
