package lang

import (
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
)

// 定义语言枚举常量
type Language int

// 顺序不可更改
const (
	English           Language = iota // 英语在最前面
	SimplifiedChinese                 // 简体中文
	Japanese                          // 日语
)

// 定义一个映射，语言枚举对应的语言代码和名称
var Languages = map[Language]struct {
	BaseCode    string
	CountryCode string
	Name        string
}{
	English:           {"en", "US", "English"},
	SimplifiedChinese: {"zh", "CN", "中文 (简体)"},
	Japanese:          {"ja", "JP", "日本語"},
}

var OrderedLanguages = []Language{
	SimplifiedChinese,
	English,
	Japanese,
}

// 可用的语言（代表有资源的语言）
var AvailableLanguages = map[Language]struct {
	BaseCode    string
	CountryCode string
	Name        string
}{
	English:           {"en", "US", "English"},
	SimplifiedChinese: {"zh", "CN", "中文 (简体)"},
	Japanese:          {"ja", "JP", "日本語"},
}

// 语种到可用语言的 fallback 映射
var LanguageFamilyFallback = map[string]Language{
	"zh": SimplifiedChinese, // 中文都 fallback 到简体
	"en": English,
	"ja": Japanese,
	// 可扩展更多语种
}

// 获取目标语言
func GetTargetLanguage(c *gin.Context) Language {
	return GetPreferredLanguage(c.GetHeader("targetLangCode"))
}

// 获取原生语言
func GetNativeLanguage(c *gin.Context) Language {
	return GetPreferredLanguage(c.GetHeader("nativeLangCode"))
}

// 解析 Accept-Language 头并返回客户端首选的语言
func GetPreferredLanguage(acceptLang string) Language {
	if acceptLang == "" {
		return English // 默认
	}
	langPairs := strings.Split(acceptLang, ",")
	for _, langPair := range langPairs {
		langWithQ := strings.Split(strings.TrimSpace(langPair), ";")
		lang := langWithQ[0]
		// 精确匹配可用语言
		for l, info := range AvailableLanguages {
			if lang == info.BaseCode {
				return l
			}
		}
		// 语种 fallback
		baseLang := strings.Split(lang, "-")[0]
		if fallback, ok := LanguageFamilyFallback[baseLang]; ok {
			if _, exist := AvailableLanguages[fallback]; exist {
				return fallback
			}
		}
	}
	return English
}

// 获取语言代码和名称
func (l Language) String() string {
	lang := Languages[l]
	return fmt.Sprintf("Code: %s, Name: %s", l.Code(), lang.Name)
}
func (l Language) Code() string {
	lang := Languages[l]
	return lang.BaseCode + "-" + lang.CountryCode
}
func (l Language) Name() string {
	lang := Languages[l]
	return lang.Name
}
