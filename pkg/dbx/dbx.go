package dbx

import (
	"context"
	"errors"
	"fmt"
	"loop/pkg/util"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// ==================== 兼容层（临时保留旧接口） ====================

// TableNameAble 旧接口兼容
type TableNameAble interface {
	TableName() string
}

// DBExtension 兼容旧版本的结构体
type DBExtension struct {
	*gorm.DB
}

// NewDBWrapper 兼容旧版本的构造函数
func NewDBWrapper(db *gorm.DB) *DBExtension {
	return &DBExtension{DB: db}
}

// UpdateAttrs 兼容旧版本的类型
type UpdateAttrs map[string]interface{}

// 兼容旧版本的方法 - 这些方法内部使用新的Repository实现
func (r *DBExtension) GetOne(result interface{}, query interface{}, args ...interface{}) (bool, error) {
	err := r.DB.Where(query, args...).First(result).Error
	if err == gorm.ErrRecordNotFound {
		return false, nil
	}
	return err == nil, err
}

func (r *DBExtension) GetList(result interface{}, query interface{}, args ...interface{}) error {
	return r.DB.Where(query, args...).Find(result).Error
}

func (r *DBExtension) GetOrderedList(result interface{}, order string, query interface{}, args ...interface{}) error {
	return r.DB.Where(query, args...).Order(order).Find(result).Error
}

func (r *DBExtension) GetListPage(result interface{}, page, pageSize int, query interface{}, args ...interface{}) (int64, error) {
	var total int64

	// 获取总数
	if err := r.DB.Model(result).Where(query, args...).Count(&total).Error; err != nil {
		return 0, err
	}

	// 计算偏移量
	offset := (page - 1) * pageSize
	if offset < 0 {
		offset = 0
	}

	// 查询数据
	err := r.DB.Where(query, args...).Offset(offset).Limit(pageSize).Find(result).Error
	return total, err
}

func (r *DBExtension) GetOrderPage(result interface{}, order string, page, pageSize int) (int64, error) {
	return r.GetOrderQueryPage(result, order, page, pageSize, "")
}

func (r *DBExtension) GetOrderQueryPage(result interface{}, order string, page, pageSize int, query interface{}, args ...interface{}) (int64, error) {
	var total int64

	// 获取总数
	if err := r.DB.Model(result).Where(query, args...).Count(&total).Error; err != nil {
		return 0, err
	}

	// 计算偏移量
	offset := (page - 1) * pageSize
	if offset < 0 {
		offset = 0
	}

	// 查询数据
	err := r.DB.Where(query, args...).Order(order).Offset(offset).Limit(pageSize).Find(result).Error
	return total, err
}

func (r *DBExtension) GetPageRangeList(result interface{}, order string, page, pageSize int, query interface{}, args ...interface{}) error {
	offset := (page - 1) * pageSize
	if offset < 0 {
		offset = 0
	}

	db := r.DB.Where(query, args...)
	if order != "" {
		db = db.Order(order)
	}

	return db.Offset(offset).Limit(pageSize).Find(result).Error
}

func (r *DBExtension) SaveOne(value TableNameAble) error {
	return r.DB.Save(value).Error
}

func (r *DBExtension) Update(attrs interface{}, query interface{}, args ...interface{}) error {
	return r.DB.Model(attrs).Where(query, args...).Updates(attrs).Error
}

func (r *DBExtension) Count(count *int64, query interface{}) error {
	return r.DB.Model(query).Where(query).Count(count).Error
}

func (r *DBExtension) Tx(funcs ...func(db *DBExtension) error) error {
	return r.DB.Transaction(func(tx *gorm.DB) error {
		txExt := &DBExtension{DB: tx}
		for _, fn := range funcs {
			if err := fn(txExt); err != nil {
				return err
			}
		}
		return nil
	})
}

func (r *DBExtension) ExecSql(result interface{}, sql string, args ...interface{}) error {
	return r.DB.Raw(sql, args...).Scan(result).Error
}

func (r *DBExtension) DeleteMultiWithValidation(instance interface{}, ids []string) error {
	if err := util.ValidateIDs(ids, 1000); err != nil {
		return err
	}
	return r.DB.Where("id IN ?", ids).Delete(instance).Error
}

// ==================== 新的现代化数据库操作类 ====================

// Model 定义数据库模型接口
type Model interface {
	TableName() string
}

// QueryBuilder 查询构建器
type QueryBuilder[T Model] struct {
	db      *gorm.DB
	query   *gorm.DB
	model   T
	ctx     context.Context
	timeout time.Duration
}

// Repository 现代化的数据库仓库
type Repository[T Model] struct {
	db      *gorm.DB
	model   T
	timeout time.Duration
}

// PageResult 分页结果
type PageResult[T any] struct {
	Data     []T   `json:"data"`
	Total    int64 `json:"total"`
	Page     int   `json:"page"`
	PageSize int   `json:"pageSize"`
	Pages    int   `json:"pages"`
}

// NewRepository 创建新的仓库实例
func NewRepository[T Model](db *gorm.DB, model T) *Repository[T] {
	return &Repository[T]{
		db:      db,
		model:   model,
		timeout: 30 * time.Second,
	}
}

// WithTimeout 设置超时时间
func (r *Repository[T]) WithTimeout(timeout time.Duration) *Repository[T] {
	r.timeout = timeout
	return r
}

// Query 创建查询构建器
func (r *Repository[T]) Query() *QueryBuilder[T] {
	ctx, cancel := context.WithTimeout(context.Background(), r.timeout)
	_ = cancel // 避免未使用变量警告

	return &QueryBuilder[T]{
		db:      r.db,
		query:   r.db.WithContext(ctx).Model(r.model),
		model:   r.model,
		ctx:     ctx,
		timeout: r.timeout,
	}
}

// Where 添加查询条件
func (qb *QueryBuilder[T]) Where(query interface{}, args ...interface{}) *QueryBuilder[T] {
	// SQL注入检查
	if queryStr, ok := query.(string); ok {
		if util.IsSQLInjection(queryStr) {
			logrus.Error("Potential SQL injection detected:", queryStr)
			return qb
		}
	}

	qb.query = qb.query.Where(query, args...)
	return qb
}

// Order 添加排序
func (qb *QueryBuilder[T]) Order(value interface{}) *QueryBuilder[T] {
	qb.query = qb.query.Order(value)
	return qb
}

// Limit 限制数量
func (qb *QueryBuilder[T]) Limit(limit int) *QueryBuilder[T] {
	if limit > 0 && limit <= 1000 { // 防止过大的limit
		qb.query = qb.query.Limit(limit)
	}
	return qb
}

// Offset 偏移量
func (qb *QueryBuilder[T]) Offset(offset int) *QueryBuilder[T] {
	if offset >= 0 {
		qb.query = qb.query.Offset(offset)
	}
	return qb
}

// Select 选择字段
func (qb *QueryBuilder[T]) Select(query interface{}, args ...interface{}) *QueryBuilder[T] {
	qb.query = qb.query.Select(query, args...)
	return qb
}

// Joins 连接查询
func (qb *QueryBuilder[T]) Joins(query string, args ...interface{}) *QueryBuilder[T] {
	qb.query = qb.query.Joins(query, args...)
	return qb
}

// Preload 预加载关联
func (qb *QueryBuilder[T]) Preload(query string, args ...interface{}) *QueryBuilder[T] {
	qb.query = qb.query.Preload(query, args...)
	return qb
}

// Group 分组
func (qb *QueryBuilder[T]) Group(name string) *QueryBuilder[T] {
	qb.query = qb.query.Group(name)
	return qb
}

// Having 分组条件
func (qb *QueryBuilder[T]) Having(query interface{}, args ...interface{}) *QueryBuilder[T] {
	qb.query = qb.query.Having(query, args...)
	return qb
}

// ==================== 查询执行方法 ====================

// First 查询第一条记录
func (qb *QueryBuilder[T]) First() (T, error) {
	var result T
	err := qb.query.First(&result).Error

	if err == gorm.ErrRecordNotFound {
		return result, nil // 返回零值和nil错误，调用者可以检查零值
	}

	if err != nil {
		qb.logError("First", err)
		return result, err
	}

	return result, nil
}

// FirstOrCreate 查询或创建
func (qb *QueryBuilder[T]) FirstOrCreate(attrs T) (T, bool, error) {
	var result T
	err := qb.query.FirstOrCreate(&result, attrs).Error

	if err != nil {
		qb.logError("FirstOrCreate", err)
		return result, false, err
	}

	// 检查是否是新创建的记录
	isCreated := qb.query.RowsAffected > 0
	return result, isCreated, nil
}

// Find 查询多条记录
func (qb *QueryBuilder[T]) Find() ([]T, error) {
	var results []T
	err := qb.query.Find(&results).Error

	if err != nil {
		qb.logError("Find", err)
		return nil, err
	}

	return results, nil
}

// Count 统计数量
func (qb *QueryBuilder[T]) Count() (int64, error) {
	var count int64
	err := qb.query.Count(&count).Error

	if err != nil {
		qb.logError("Count", err)
		return 0, err
	}

	return count, nil
}

// Exists 检查是否存在
func (qb *QueryBuilder[T]) Exists() (bool, error) {
	count, err := qb.Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// Paginate 分页查询
func (qb *QueryBuilder[T]) Paginate(page, pageSize int) (*PageResult[T], error) {
	// 参数验证
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	if pageSize > 100 {
		pageSize = 100 // 限制最大页面大小
	}

	// 先获取总数
	total, err := qb.Count()
	if err != nil {
		return nil, err
	}

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 查询数据
	results, err := qb.Offset(offset).Limit(pageSize).Find()
	if err != nil {
		return nil, err
	}

	// 计算总页数
	pages := int((total + int64(pageSize) - 1) / int64(pageSize))

	return &PageResult[T]{
		Data:     results,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
		Pages:    pages,
	}, nil
}

// Update 更新记录
func (qb *QueryBuilder[T]) Update(column string, value interface{}) error {
	result := qb.query.Update(column, value)

	if result.Error != nil {
		qb.logError("Update", result.Error)
		return result.Error
	}

	if result.RowsAffected == 0 {
		logrus.Warn("No rows affected in Update operation")
	}

	return nil
}

// Updates 批量更新
func (qb *QueryBuilder[T]) Updates(values interface{}) error {
	result := qb.query.Updates(values)

	if result.Error != nil {
		qb.logError("Updates", result.Error)
		return result.Error
	}

	if result.RowsAffected == 0 {
		logrus.Warn("No rows affected in Updates operation")
	}

	return nil
}

// Delete 删除记录
func (qb *QueryBuilder[T]) Delete() error {
	result := qb.query.Delete(qb.model)

	if result.Error != nil {
		qb.logError("Delete", result.Error)
		return result.Error
	}

	if result.RowsAffected == 0 {
		logrus.Warn("No rows affected in Delete operation")
	}

	return nil
}

// logError 记录错误日志
func (qb *QueryBuilder[T]) logError(operation string, err error) {
	logrus.WithFields(logrus.Fields{
		"operation": operation,
		"table":     qb.model.TableName(),
		"error":     err.Error(),
		"timestamp": time.Now(),
	}).Error("Database operation failed")
}

// ==================== 仓库便捷方法 ====================

// Create 创建记录
func (r *Repository[T]) Create(model T) error {
	ctx, cancel := context.WithTimeout(context.Background(), r.timeout)
	defer cancel()

	result := r.db.WithContext(ctx).Create(model)
	if result.Error != nil {
		r.logError("Create", result.Error)
		return result.Error
	}

	return nil
}

// CreateInBatches 批量创建
func (r *Repository[T]) CreateInBatches(models []T, batchSize int) error {
	if batchSize <= 0 {
		batchSize = 100 // 默认批次大小
	}

	ctx, cancel := context.WithTimeout(context.Background(), r.timeout)
	defer cancel()

	result := r.db.WithContext(ctx).CreateInBatches(models, batchSize)
	if result.Error != nil {
		r.logError("CreateInBatches", result.Error)
		return result.Error
	}

	return nil
}

// Save 保存记录（创建或更新）
func (r *Repository[T]) Save(model T) error {
	ctx, cancel := context.WithTimeout(context.Background(), r.timeout)
	defer cancel()

	result := r.db.WithContext(ctx).Save(model)
	if result.Error != nil {
		r.logError("Save", result.Error)
		return result.Error
	}

	return nil
}

// FindByID 根据ID查询
func (r *Repository[T]) FindByID(id interface{}) (T, error) {
	return r.Query().Where("id = ?", id).First()
}

// FindAll 查询所有记录
func (r *Repository[T]) FindAll() ([]T, error) {
	return r.Query().Find()
}

// DeleteByID 根据ID删除
func (r *Repository[T]) DeleteByID(id interface{}) error {
	return r.Query().Where("id = ?", id).Delete()
}

// DeleteByIDs 根据ID列表批量删除
func (r *Repository[T]) DeleteByIDs(ids []interface{}) error {
	if len(ids) == 0 {
		return nil
	}

	// 验证ID数量限制
	if len(ids) > 1000 {
		return errors.New("too many IDs for batch delete, maximum 1000 allowed")
	}

	return r.Query().Where("id IN ?", ids).Delete()
}

// Upsert 插入或更新
func (r *Repository[T]) Upsert(model T, conflictColumns []string, updateColumns []string) error {
	ctx, cancel := context.WithTimeout(context.Background(), r.timeout)
	defer cancel()

	columns := make([]clause.Column, len(conflictColumns))
	for i, col := range conflictColumns {
		columns[i] = clause.Column{Name: col}
	}

	result := r.db.WithContext(ctx).Clauses(clause.OnConflict{
		Columns:   columns,
		DoUpdates: clause.AssignmentColumns(updateColumns),
	}).Create(model)

	if result.Error != nil {
		r.logError("Upsert", result.Error)
		return result.Error
	}

	return nil
}

// Transaction 事务处理
func (r *Repository[T]) Transaction(fn func(*Repository[T]) error) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		txRepo := &Repository[T]{
			db:      tx,
			model:   r.model,
			timeout: r.timeout,
		}
		return fn(txRepo)
	})
}

// logError 记录错误日志
func (r *Repository[T]) logError(operation string, err error) {
	logrus.WithFields(logrus.Fields{
		"operation": operation,
		"table":     r.model.TableName(),
		"error":     err.Error(),
		"timestamp": time.Now(),
	}).Error("Repository operation failed")
}

// ==================== 高级功能和工具方法 ====================

// Scope 定义查询作用域类型
type Scope[T Model] func(*QueryBuilder[T]) *QueryBuilder[T]

// Scopes 应用多个作用域
func (qb *QueryBuilder[T]) Scopes(scopes ...Scope[T]) *QueryBuilder[T] {
	for _, scope := range scopes {
		qb = scope(qb)
	}
	return qb
}

// 常用的作用域函数

// ActiveScope 只查询活跃记录（假设有status字段）
func ActiveScope[T Model]() Scope[T] {
	return func(qb *QueryBuilder[T]) *QueryBuilder[T] {
		return qb.Where("status = ?", "active")
	}
}

// CreatedAfterScope 查询指定时间后创建的记录
func CreatedAfterScope[T Model](after time.Time) Scope[T] {
	return func(qb *QueryBuilder[T]) *QueryBuilder[T] {
		return qb.Where("created_at > ?", after)
	}
}

// OrderByIDDesc 按ID降序排列
func OrderByIDDesc[T Model]() Scope[T] {
	return func(qb *QueryBuilder[T]) *QueryBuilder[T] {
		return qb.Order("id DESC")
	}
}

// ==================== 缓存支持 ====================

// CacheConfig 缓存配置
type CacheConfig struct {
	TTL    time.Duration
	Prefix string
}

// WithCache 添加缓存支持（需要配合Redis等缓存中间件）
func (qb *QueryBuilder[T]) WithCache(config CacheConfig) *QueryBuilder[T] {
	// 这里可以集成缓存逻辑
	// 实际实现需要根据具体的缓存方案来定制
	return qb
}

// ==================== 统计和聚合方法 ====================

// Sum 求和
func (qb *QueryBuilder[T]) Sum(column string) (float64, error) {
	var result float64
	err := qb.query.Select(fmt.Sprintf("SUM(%s)", column)).Row().Scan(&result)

	if err != nil {
		qb.logError("Sum", err)
		return 0, err
	}

	return result, nil
}

// Avg 平均值
func (qb *QueryBuilder[T]) Avg(column string) (float64, error) {
	var result float64
	err := qb.query.Select(fmt.Sprintf("AVG(%s)", column)).Row().Scan(&result)

	if err != nil {
		qb.logError("Avg", err)
		return 0, err
	}

	return result, nil
}

// Max 最大值
func (qb *QueryBuilder[T]) Max(column string) (interface{}, error) {
	var result interface{}
	err := qb.query.Select(fmt.Sprintf("MAX(%s)", column)).Row().Scan(&result)

	if err != nil {
		qb.logError("Max", err)
		return nil, err
	}

	return result, nil
}

// Min 最小值
func (qb *QueryBuilder[T]) Min(column string) (interface{}, error) {
	var result interface{}
	err := qb.query.Select(fmt.Sprintf("MIN(%s)", column)).Row().Scan(&result)

	if err != nil {
		qb.logError("Min", err)
		return nil, err
	}

	return result, nil
}

// ==================== 原生SQL支持 ====================

// Raw 执行原生SQL查询
func (r *Repository[T]) Raw(sql string, args ...interface{}) *QueryBuilder[T] {
	// SQL注入检查
	if util.IsSQLInjection(sql) {
		logrus.Error("Potential SQL injection detected in Raw SQL:", sql)
		return r.Query() // 返回空查询
	}

	ctx, cancel := context.WithTimeout(context.Background(), r.timeout)
	_ = cancel

	qb := &QueryBuilder[T]{
		db:      r.db,
		query:   r.db.WithContext(ctx).Raw(sql, args...),
		model:   r.model,
		ctx:     ctx,
		timeout: r.timeout,
	}

	return qb
}

// Exec 执行原生SQL（INSERT, UPDATE, DELETE等）
func (r *Repository[T]) Exec(sql string, args ...interface{}) error {
	// SQL注入检查
	if util.IsSQLInjection(sql) {
		return errors.New("potential SQL injection detected")
	}

	ctx, cancel := context.WithTimeout(context.Background(), r.timeout)
	defer cancel()

	result := r.db.WithContext(ctx).Exec(sql, args...)
	if result.Error != nil {
		r.logError("Exec", result.Error)
		return result.Error
	}

	return nil
}

// ==================== 工厂方法和便捷函数 ====================

// DB 全局数据库实例（需要在应用启动时初始化）
var DB *gorm.DB

// SetDB 设置全局数据库实例
func SetDB(db *gorm.DB) {
	DB = db
}

// NewRepo 创建新的仓库实例的便捷方法
func NewRepo[T Model](model T) *Repository[T] {
	if DB == nil {
		panic("Database not initialized. Call SetDB() first.")
	}
	return NewRepository(DB, model)
}

// ==================== 使用示例和最佳实践 ====================

/*
使用示例：

1. 基本查询：
   userRepo := NewRepo(User{})
   user, err := userRepo.FindByID(1)

2. 复杂查询：
   users, err := userRepo.Query().
       Where("age > ?", 18).
       Where("status = ?", "active").
       Order("created_at DESC").
       Limit(10).
       Find()

3. 分页查询：
   result, err := userRepo.Query().
       Where("department = ?", "IT").
       Paginate(1, 20)

4. 使用作用域：
   users, err := userRepo.Query().
       Scopes(ActiveScope[User](), OrderByIDDesc[User]()).
       Find()

5. 事务处理：
   err := userRepo.Transaction(func(tx *Repository[User]) error {
       if err := tx.Create(user1); err != nil {
           return err
       }
       return tx.Create(user2)
   })

6. 批量操作：
   err := userRepo.CreateInBatches(users, 100)
   err = userRepo.DeleteByIDs([]interface{}{1, 2, 3})

7. 统计查询：
   count, err := userRepo.Query().Where("status = ?", "active").Count()
   avgAge, err := userRepo.Query().Avg("age")

8. 原生SQL：
   users, err := userRepo.Raw("SELECT * FROM users WHERE custom_condition = ?", value).Find()

最佳实践：
- 总是检查错误
- 使用事务处理相关操作
- 合理设置超时时间
- 使用作用域复用查询逻辑
- 避免N+1查询，使用Preload
- 对于大量数据使用批量操作
- 定期监控慢查询日志
*/
