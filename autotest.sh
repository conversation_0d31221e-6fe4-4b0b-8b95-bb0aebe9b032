#!/bin/bash

# 检查是否传递了 -f 参数
FORCE_CLEAN=false
while getopts "f" opt; do
    case $opt in
        f)
            FORCE_CLEAN=true
            ;;
        *)
            echo "Usage: $0 [-f]"
            exit 1
            ;;
    esac
done

# 自动化测试环境启动脚本
echo "启动自动化测试环境..."

# 设置环境变量
export TZ=Asia/Shanghai
export APP_TEST_PORT=7000

# 设置logs目录权限
echo "设置logs目录权限..."
sudo chmod 777 /var/log/looptest

# 停止并删除测试容器（忽略错误）
echo "停止并删除旧测试容器..."
docker stop loop-test || true
docker rm loop-test || true
docker stop mysql-test || true
docker rm mysql-test || true
docker stop redis-test || true
docker rm redis-test || true

# 删除旧测试镜像（忽略错误）
echo "删除旧测试镜像..."
docker rmi loop-test:v1 || true

# 清理悬空镜像（避免无镜像时报错）
echo "清理悬空镜像..."
docker images | grep "none" | awk '{print $3}' | xargs -r docker rmi || true

# 启动测试服务
echo "开始构建和启动测试服务..."
docker compose -f docker-compose-test.yaml down --rmi local  # 清理 compose 管理的镜像
docker compose -f docker-compose-test.yaml up -d --build     # 强制重建镜像并启动

# 如果传递了 -f 参数，则清理缓存和未使用的资源
if [ "$FORCE_CLEAN" = true ]; then
    echo "正在强制清理docker的缓存数据和无用数据..."
    docker builder prune -f
    docker volume prune -f
    docker system prune -a -f
fi

# 等待数据库准备就绪
echo "等待数据库准备就绪..."
max_attempts=30
attempt=1
while [ $attempt -le $max_attempts ]; do
    if docker exec mysql-test mysqladmin ping -h localhost -u root -p123456 --silent; then
        echo "数据库已准备就绪！"
        break
    else
        echo "等待数据库启动... (尝试 $attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    fi
done

if [ $attempt -gt $max_attempts ]; then
    echo "错误：数据库启动超时！"
    exit 1
fi

# 等待应用容器启动
echo "等待应用容器启动..."
sleep 10

# 执行初始化管理员数据
echo "开始初始化管理员数据..."
if [ -f "scripts/init/init_admin_data.go" ]; then
    # 检查Go环境
    if ! command -v go &> /dev/null; then
        echo "警告：未找到Go环境，使用Docker容器内编译的二进制文件..."
        
        # 在Docker容器内执行编译好的二进制文件
        echo "在Docker容器内执行初始化脚本..."
        docker exec loop-test sh -c "cd /app && ./init_admin_data -init-config configs/config-auto-test.yaml"
    else
        # 创建临时配置文件，使用localhost连接数据库
        echo "创建临时配置文件..."
        temp_config_file="configs/config-auto-test-localhost.yaml"
        cp configs/config-auto-test.yaml "$temp_config_file"
        
        # 替换数据库连接字符串为localhost
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            sed -i '' 's/mysql-test:3306/localhost:8766/g' "$temp_config_file"
        else
            # Linux
            sed -i 's/mysql-test:3306/localhost:8766/g' "$temp_config_file"
        fi
        
        # 在宿主机上执行初始化脚本
        echo "在宿主机上执行初始化脚本..."
        cd scripts/init
        go run init_admin_data.go -init-config "../../$temp_config_file"
        cd ../..
        
        # 清理临时配置文件
        rm -f "$temp_config_file"
    fi
    
    if [ $? -eq 0 ]; then
        echo "管理员数据初始化完成！"
    else
        echo "错误：管理员数据初始化失败！"
        exit 1
    fi
else
    echo "错误：找不到 init_admin_data.go 文件"
    exit 1
fi

echo "测试环境启动完成！"
echo "应用端口: 7000"
echo "MySQL端口: 7002"
echo "Redis端口: 7001"
echo ""
echo "容器名称:"
echo "- 应用: loop-test"
echo "- MySQL: mysql-test"
echo "- Redis: redis-test"
echo ""
echo "查看日志:"
echo "docker logs loop-test"
echo "docker logs mysql-test"
echo "docker logs redis-test"

# 添加诊断步骤
echo ""
echo "=== 执行诊断检查 ==="

# 检查容器状态
echo "1. 检查容器状态："
docker ps -a

# 检查应用是否正常启动
echo ""
echo "2. 检查应用启动状态："
if docker exec loop-test sh -c "ps aux | grep main" 2>/dev/null | grep -v grep; then
    echo "✓ 应用进程正在运行"
else
    echo "✗ 应用进程未运行"
    echo "查看应用日志："
    docker logs loop-test --tail 20
fi

# 测试本地连接
echo ""
echo "3. 测试本地连接："
if curl -s http://localhost:8765/api/v1/langs > /dev/null; then
    echo "✓ 本地连接成功"
else
    echo "✗ 本地连接失败"
fi

# 检查端口监听
echo ""
echo "4. 检查端口监听："
netstat -tlnp | grep 8765 || echo "端口8765未监听"

echo ""
echo "=== 诊断完成 ==="
echo ""
echo "如果应用未正常启动，请检查："
echo "1. 数据库连接：docker logs mysql-test"
echo "2. 应用日志：docker logs loop-test"
echo "3. 网络连接：docker network ls"
echo "4. 重启应用：docker restart loop-test"