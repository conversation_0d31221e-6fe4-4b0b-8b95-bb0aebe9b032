import requests
import time
import os

base_url = 'https://openspeech.bytedance.com/api/v1/vc/submit'
query_url = 'https://openspeech.bytedance.com/api/v1/vc/query'
appid = "4690637193"
access_token = "-f2KhqVBkSnVoikP45roVAeJtSnIJY2S"
language = 'en-US'
audio_file_path = '/Users/<USER>/Downloads/output.mp3'  # 修改为你的本地音频文件路径

# 1. 提交音频
with open(audio_file_path, 'rb') as f:
    audio_data = f.read()

response = requests.post(
    base_url,
    params=dict(
        appid=appid,
        language=language,
        use_itn='True',
        use_capitalize='True',
        max_lines=10,            # 适当增大
        words_per_line=100,      # 适当减小
        caption_type='speech',  # 只识别说话
        use_punc='True',        # 增加标点
    ),
    data=audio_data,
    headers={
        'content-type': 'audio/mp3',
        'Authorization': 'Bearer; {}'.format(access_token)
    }
)
print('submit response = {}'.format(response.text))
result = response.json()
job_id = result.get('id')
if not job_id:
    print('提交失败，未获取到任务ID')
    exit(1)

# 2. 查询结果（轮询，直到成功或出错）
while True:
    query_resp = requests.get(
        query_url,
        params=dict(
            appid=appid,
            id=job_id,
        ),
        headers={
            'Authorization': 'Bearer; {}'.format(access_token)
        }
    )
    data = query_resp.json()
    print('query response =', data)
    if data.get('message') == 'Success':
        print('识别完成，字幕内容如下：')
        for utt in data.get('utterances', []):
            print(utt.get('text', ''))
        break
    elif data.get('code') == 2000:
        print('任务处理中，等待2秒后重试...')
        time.sleep(2)
    else:
        print('识别失败或出错：', data)
        break