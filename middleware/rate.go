package middleware

import (
	"loop/pkg/jwtx"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/time/rate"
)

// 初始化全局限流器（每秒10个令牌，桶容量30）
// var globalLimiter = rate.NewLimiter(rate.Limit(100), 200)

// func GlobalLimitMiddleware(c *gin.Context) {
// 	if !globalLimiter.Allow() {
// 		c.AbortWithStatusJSON(429, gin.H{"error": "global limit exceeded"})
// 		return
// 	}
// 	c.Next()
// }

var (
	ipLimiters = make(map[string]*rate.Limiter)
	mu         sync.RWMutex
)

func getIPLimiter(ip string) *rate.Limiter {
	mu.Lock()
	defer mu.Unlock()

	limiter, exists := ipLimiters[ip]
	if !exists {
		// 每个IP限制为5请求/秒，突发10个
		limiter = rate.NewLimiter(rate.Every(time.Second), 20)
		ipLimiters[ip] = limiter
		// 定时清理（可选）
		time.AfterFunc(5*time.Minute, func() {
			mu.Lock()
			delete(ipLimiters, ip)
			mu.Unlock()
		})
	}
	return limiter
}

func IPLimitMiddleware(c *gin.Context) {
	limiter := getIPLimiter(c.ClientIP())
	if !limiter.Allow() {
		c.AbortWithStatusJSON(429, gin.H{"error": "ip limit exceeded"})
		return
	}
	c.Next()
}

var userLimiters = make(map[string]*rate.Limiter)
var userMu sync.RWMutex

func getUserLimiter(userID string) *rate.Limiter {
	userMu.Lock()
	defer userMu.Unlock()

	if limiter, exists := userLimiters[userID]; exists {
		return limiter
	}
	// 每个用户限制为50/秒，突发50
	// limiter := rate.NewLimiter(3, 3)
	// 	第一个参数 3：表示每秒可以产生3个令牌（token），也就是每秒最多允许3次请求。
	// 第二个参数 3：表示桶的容量（burst），也就是最多可以瞬间处理3个请求，如果桶满了就不再积攒。
	limiter := rate.NewLimiter(rate.Every(time.Second), 50)
	userLimiters[userID] = limiter
	return limiter
}

func UserLimitMiddleware(c *gin.Context) {
	userID := jwtx.GetUid(c)
	if userID == "" {
		c.Next()
		return
	}

	if !getUserLimiter(userID).Allow() {
		c.AbortWithStatusJSON(429, gin.H{"error": "user limit exceeded"})
		return
	}
	c.Next()
}
