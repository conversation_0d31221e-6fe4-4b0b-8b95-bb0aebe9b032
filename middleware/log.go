package middleware

import (
	"bytes"
	"fmt"
	"io"
	"math"
	"net/http"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

var timeFormat = "02/Jan/2006:15:04:05 -0700"

// 日志记录到文件
func LoggerToFile(notLogged ...string) gin.HandlerFunc {
	hostname, err := os.Hostname()
	if err != nil {
		hostname = "unknow"
	}

	var skip map[string]struct{}

	if length := len(notLogged); length > 0 {
		skip = make(map[string]struct{}, length)

		for _, p := range notLogged {
			skip[p] = struct{}{}
		}
	}
	return func(c *gin.Context) {
		// other handler can change c.Path so:
		path := c.Request.URL.Path
		start := time.Now()

		// 获取请求参数
		queryParams := c.Request.URL.Query()
		formParams := c.Request.PostForm
		// 读取请求的 JSON body
		var requestBody []byte
		if c.Request.Method == http.MethodPost || c.Request.Method == http.MethodPut {
			contentType := c.Request.Header.Get("Content-Type")
			if contentType == "application/json" {
				bodyBytes, err := io.ReadAll(c.Request.Body)
				if err == nil {
					requestBody = bodyBytes
					c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
				}
			}
		}

		clientIP := c.ClientIP()
		clientUserAgent := c.Request.UserAgent()
		referer := c.Request.Referer()

		if _, ok := skip[path]; ok {
			c.Next()
			return
		}

		// 在请求开始时打印访问日志
		logrus.WithFields(logrus.Fields{
			"type":       "request_start",
			"method":     c.Request.Method,
			"path":       path,
			"client_ip":  clientIP,
			"user_agent": clientUserAgent,
			"referer":    referer,
			"query":      queryParams,
			"form":       formParams,
			"body":       string(requestBody),
			"hostname":   hostname,
		}).Info("HTTP Request Started")

		// 执行后续中间件和处理器
		c.Next()

		// 在请求结束时打印响应日志
		stop := time.Since(start)
		latency := int(math.Ceil(float64(stop.Nanoseconds()) / 1000000.0))
		statusCode := c.Writer.Status()
		dataLength := c.Writer.Size()
		if dataLength < 0 {
			dataLength = 0
		}

		// 根据状态码选择日志级别
		var logLevel logrus.Level
		switch {
		case statusCode >= http.StatusInternalServerError:
			logLevel = logrus.ErrorLevel
		case statusCode >= http.StatusBadRequest:
			logLevel = logrus.WarnLevel
		default:
			logLevel = logrus.InfoLevel
		}

		// 构建响应日志字段
		fields := logrus.Fields{
			"type":        "request_end",
			"method":      c.Request.Method,
			"path":        path,
			"status_code": statusCode,
			"latency_ms":  latency,
			"data_length": dataLength,
			"client_ip":   clientIP,
			"user_agent":  clientUserAgent,
			"referer":     referer,
			"hostname":    hostname,
		}

		// 如果有错误，添加错误信息
		if len(c.Errors) > 0 {
			fields["errors"] = c.Errors.ByType(gin.ErrorTypePrivate).String()
		}

		// 打印响应日志
		logrus.WithFields(fields).Log(logLevel, fmt.Sprintf("HTTP Request Completed - %s %s %d (%dms)",
			c.Request.Method, path, statusCode, latency))
	}
}
