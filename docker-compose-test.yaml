version: '3.3'

services:
  app-test:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: loop-test
    restart: always
    ports:
      - "8765:3000"
    environment:
      - TZ=${TZ}
      - OSS_ACCESS_KEY_ID=${OSS_ACCESS_KEY_ID}
      - OSS_ACCESS_KEY_SECRET=${OSS_ACCESS_KEY_SECRET}
      - REDIS_HOST=redis-test
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    depends_on:
      - mysql-test
      - redis-test
    networks:
      - loop_test_network
    volumes:
      - /var/log/looptest/:/var/log/looptest/
    command: ["./main", "-config=/app/configs/config-auto-test.yaml"]

  mysql-test:
    container_name: mysql-test
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=123456
      - MYSQL_DATABASE=loop_test
    ports:
      - "8766:3306"
    networks:
      - loop_test_network
    volumes:
      - mysql_test_data:/var/lib/mysql
    restart: always

  redis-test:
    image: redis:latest
    container_name: redis-test
    ports:
      - "8767:6379"
    environment:
      TZ: ${TZ}
    command: >
      redis-server 
      --requirepass ${REDIS_PASSWORD}
      --appendonly yes
      --bind 0.0.0.0
      --protected-mode no
    volumes:
      - redis_test_data:/data
    privileged: true
    restart: always
    networks:
      - loop_test_network
    # 如果需要自定义 Redis 配置
    # configs:
    #   - source: redis_config
    #     target: /usr/local/etc/redis/redis.conf

networks:
  loop_test_network:
    driver: bridge

volumes:
  mysql_test_data:
    driver: local
  redis_test_data:
    driver: local

# 如果需要自定义 Redis 配置
# configs:
#   redis_config:
#     file: ./redis.conf 