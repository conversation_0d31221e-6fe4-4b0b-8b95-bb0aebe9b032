name: Automated API Tests

on: [push, pull_request]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js environment
      uses: actions/setup-node@v2
      with:
        node-version: '14'

    - name: Install Apifox CLI
      run: npm install -g apifox-cli

    - name: 普通会员兑换码测试
      run: apifox run --access-token APS-gQbwMKHzkvA263dasSiGOz9BdxKsyr0y -t 7052026 -e 36504268 -n 1 -r html,cli --notification 311796 
    - name: 用户注销账号测试
      run: apifox run --access-token APS-gQbwMKHzkvA263dasSiGOz9BdxKsyr0y -t 6851422 -e 36504268 -n 1 -r html,cli --notification 311796