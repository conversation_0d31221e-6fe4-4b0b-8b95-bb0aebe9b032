# 会员系统测试文档

## 1. 测试概述

### 1.1 测试目标
本测试文档旨在确保会员系统的各个模块功能正常，包括会员管理、支付处理、权益分配、兑换码系统等核心功能的正确性和稳定性。

### 1.2 测试范围
- 会员管理模块
- 支付处理模块
- 权益系统模块
- 兑换码系统
- 苹果内购通知处理
- 数据库操作
- API接口功能

### 1.3 测试环境要求
- Go 1.19+
- MySQL 8.0+
- Redis (可选，用于缓存测试)
- 苹果开发者账号 (用于内购测试)

## 2. 单元测试

### 2.1 会员模型测试 (internal/model/vip.go)

#### 2.1.1 会员等级常量测试
```go
func TestVipLevelConstants(t *testing.T) {
    // 测试会员等级常量定义
    assert.Equal(t, 1, model.VIP_LEVEL_NORMAL)
    assert.Equal(t, 100, model.VIP_LEVEL_PRO)
    assert.Equal(t, 1000, model.VIP_LEVEL_ULTITA)
}
```

#### 2.1.2 订单状态常量测试
```go
func TestOrderStatusConstants(t *testing.T) {
    // 测试订单状态常量
    assert.Equal(t, 1, model.TradeOrderStatusWaitPay)
    assert.Equal(t, 2, model.TradeOrderStatusPayFinish)
    assert.Equal(t, 3, model.TradeOrderStatusPayFailed)
}
```

#### 2.1.3 订阅状态常量测试
```go
func TestSubscriptionStatusConstants(t *testing.T) {
    // 测试订阅状态常量
    assert.Equal(t, 1, model.UserSubscriptionStatusContract)
    assert.Equal(t, 2, model.UserSubscriptionStatusTermination)
}
```

### 2.2 权益模型测试 (internal/model/benefit.go)

#### 2.2.1 权益创建测试
```go
func TestCreateBenefit(t *testing.T) {
    // 测试权益创建功能
    benefit := &model.Benefit{
        Name:           "测试权益",
        Code:           "TEST_BENEFIT",
        Level:          10,
        CycleType:      1,
        CycleCount:     1,
        BenefitCount:   5,
        Sort:           1,
        Status:         1,
        BenefitGroupID: 1,
        Description:    "测试权益描述",
    }
    
    err := benefitModel.CreateBenefit(benefit)
    assert.NoError(t, err)
    assert.NotZero(t, benefit.Id)
}
```

#### 2.2.2 权益查询测试
```go
func TestGetBenefitByID(t *testing.T) {
    // 测试根据ID获取权益
    benefit, err := benefitModel.GetBenefitByID(1)
    assert.NoError(t, err)
    assert.NotNil(t, benefit)
    assert.Equal(t, "测试权益", benefit.Name)
}
```

#### 2.2.3 用户权益分配测试
```go
func TestAssignBenefitsByVipLevel(t *testing.T) {
    // 测试根据会员等级分配权益
    uid := "test_user_123"
    vipLevel := 100 // PRO会员
    
    err := benefitModel.AssignBenefitsByVipLevel(uid, vipLevel)
    assert.NoError(t, err)
    
    // 验证权益是否分配成功
    userBenefits, err := benefitModel.GetUserBenefits(uid)
    assert.NoError(t, err)
    assert.NotEmpty(t, userBenefits)
}
```

#### 2.2.4 权益消耗测试
```go
func TestConsumeBenefit(t *testing.T) {
    // 测试权益消耗功能
    uid := "test_user_123"
    benefitGroupCode := "AI_TRANSLATION"
    count := 1
    
    err := benefitModel.ConsumeBenefit(uid, benefitGroupCode, count)
    assert.NoError(t, err)
    
    // 验证剩余数量是否正确
    userBenefit, err := benefitModel.GetUserBenefitByGroup(uid, benefitGroupCode)
    assert.NoError(t, err)
    assert.NotEmpty(t, userBenefit)
    // 验证剩余数量减少
}
```

### 2.3 数据层测试 (internal/data/)

#### 2.3.1 会员数据层测试
```go
func TestVipRepo_CreatePayOrder(t *testing.T) {
    // 测试创建支付订单
    req := request.CreatePayOrderReq{
        ProductId: 1,
    }
    
    result := vipRepo.CreatePayOrder(context, req)
    assert.True(t, result.Success)
    assert.NotEmpty(t, result.Data.(response.CreatePayOrderResp).UUID)
}
```

#### 2.3.2 权益数据层测试
```go
func TestBenefitRepo_GetUserBenefits(t *testing.T) {
    // 测试获取用户权益
    result := benefitRepo.GetUserBenefits(context)
    assert.True(t, result.Success)
    
    benefits := result.Data.([]*model.UserBenefit)
    assert.NotNil(t, benefits)
}
```

## 3. 集成测试

### 3.1 API接口测试

#### 3.1.1 会员相关接口测试

********* 获取商品列表**
```go
func TestGetProductList(t *testing.T) {
    // 测试获取商品列表接口
    req := httptest.NewRequest("GET", "/api/vip/products", nil)
    w := httptest.NewRecorder()
    
    router.ServeHTTP(w, req)
    
    assert.Equal(t, http.StatusOK, w.Code)
    
    var response map[string]interface{}
    json.Unmarshal(w.Body.Bytes(), &response)
    assert.True(t, response["success"].(bool))
}
```

********* 创建支付订单**
```go
func TestCreatePayOrder(t *testing.T) {
    // 测试创建支付订单接口
    reqBody := `{"productId": 1}`
    req := httptest.NewRequest("POST", "/api/vip/createOrder", strings.NewReader(reqBody))
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("Authorization", "Bearer " + testToken)
    
    w := httptest.NewRecorder()
    router.ServeHTTP(w, req)
    
    assert.Equal(t, http.StatusOK, w.Code)
    
    var response map[string]interface{}
    json.Unmarshal(w.Body.Bytes(), &response)
    assert.True(t, response["success"].(bool))
    assert.NotEmpty(t, response["data"].(map[string]interface{})["uuid"])
}
```

********* 兑换码接口**
```go
func TestExchangeCode(t *testing.T) {
    // 测试兑换码接口
    reqBody := `{"code": "TEST_CODE_123"}`
    req := httptest.NewRequest("POST", "/api/vip/exchangeCode", strings.NewReader(reqBody))
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("Authorization", "Bearer " + testToken)
    
    w := httptest.NewRecorder()
    router.ServeHTTP(w, req)
    
    assert.Equal(t, http.StatusOK, w.Code)
    
    var response map[string]interface{}
    json.Unmarshal(w.Body.Bytes(), &response)
    assert.True(t, response["success"].(bool))
}
```

********* 获取用户会员信息**
```go
func TestGetUserVipInfo(t *testing.T) {
    // 测试获取用户会员信息接口
    req := httptest.NewRequest("GET", "/api/vip/info", nil)
    req.Header.Set("Authorization", "Bearer " + testToken)
    
    w := httptest.NewRecorder()
    router.ServeHTTP(w, req)
    
    assert.Equal(t, http.StatusOK, w.Code)
    
    var response map[string]interface{}
    json.Unmarshal(w.Body.Bytes(), &response)
    assert.True(t, response["success"].(bool))
}
```

#### 3.1.2 权益相关接口测试

********* 获取所有权益**
```go
func TestGetAllBenefits(t *testing.T) {
    // 测试获取所有权益接口
    req := httptest.NewRequest("GET", "/api/admin/benefits?currentPage=1&pageSize=10", nil)
    req.Header.Set("Authorization", "Bearer " + adminToken)
    
    w := httptest.NewRecorder()
    router.ServeHTTP(w, req)
    
    assert.Equal(t, http.StatusOK, w.Code)
    
    var response map[string]interface{}
    json.Unmarshal(w.Body.Bytes(), &response)
    assert.True(t, response["success"].(bool))
}
```

********* 创建权益**
```go
func TestCreateBenefit(t *testing.T) {
    // 测试创建权益接口
    reqBody := `{
        "name": "测试权益",
        "code": "TEST_BENEFIT",
        "level": 10,
        "cycleType": 1,
        "cycleCount": 1,
        "benefitCount": 5,
        "sort": 1,
        "status": 1,
        "benefitGroupId": 1,
        "description": "测试权益描述"
    }`
    
    req := httptest.NewRequest("POST", "/api/admin/benefit", strings.NewReader(reqBody))
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("Authorization", "Bearer " + adminToken)
    
    w := httptest.NewRecorder()
    router.ServeHTTP(w, req)
    
    assert.Equal(t, http.StatusOK, w.Code)
    
    var response map[string]interface{}
    json.Unmarshal(w.Body.Bytes(), &response)
    assert.True(t, response["success"].(bool))
}
```

### 3.2 数据库集成测试

#### 3.2.1 会员流水测试
```go
func TestUserVIPFlow(t *testing.T) {
    // 测试会员流水记录
    flow := &model.UserVIPFlow{
        Uid:                "test_user_123",
        Title:              "购买会员",
        Type:               1, // 订阅
        Operation:          1, // 开通
        OperationTimestamp: time.Now().UnixMilli(),
        Days:               30,
        Terminal:           2, // iOS
        BizID:              "order_123",
    }
    
    err := db.Create(flow).Error
    assert.NoError(t, err)
    assert.NotZero(t, flow.Id)
}
```

#### 3.2.2 订阅记录测试
```go
func TestUserSubscription(t *testing.T) {
    // 测试订阅记录
    subscription := &model.UserSubscription{
        Uid:                        "test_user_123",
        ProductID:                  1,
        ProductName:                "月度会员",
        PaymentProvider:            1, // 苹果内购
        OutTransactionId:           "transaction_123",
        AppleOriginalTransactionId: "original_transaction_123",
        FirstCycleAmount:           9.99,
        NextCycleAmount:            9.99,
        NextPaidDate:               "2024-02-01",
        NextPaidTimestamp:          time.Now().AddDate(0, 1, 0).UnixMilli(),
        SignTimestamp:              time.Now().UnixMilli(),
        Status:                     1, // 签约
        Currency:                   "USD",
        Desc:                       "正常订阅",
    }
    
    err := db.Create(subscription).Error
    assert.NoError(t, err)
    assert.NotZero(t, subscription.Id)
}
``` 

## 4. 业务流程测试

### 4.1 会员购买流程测试

#### 4.1.1 完整购买流程
```go
func TestCompletePurchaseFlow(t *testing.T) {
    // 1. 创建支付订单
    orderNo, err := vipModel.CreatePayOrder("test_user_123", 1, 2)
    assert.NoError(t, err)
    assert.NotEmpty(t, orderNo)
    
    // 2. 模拟苹果支付通知
    notification := &apple.NotificationV2Payload{
        // 模拟苹果通知数据
    }
    
    err = vipModel.ReceiveApplePayResult(notification, nil, nil)
    assert.NoError(t, err)
    
    // 3. 验证会员状态
    userVip, found, err := vipModel.GetUserVIPRelations("test_user_123")
    assert.NoError(t, err)
    assert.True(t, found)
    assert.Equal(t, 1, userVip.IsVip)
    assert.Equal(t, uint(100), userVip.VipID) // PRO会员
    
    // 4. 验证权益分配
    userBenefits, err := benefitModel.GetUserBenefits("test_user_123")
    assert.NoError(t, err)
    assert.NotEmpty(t, userBenefits)
}
```

### 4.2 兑换码流程测试

#### 4.2.1 兑换码使用流程
```go
func TestExchangeCodeFlow(t *testing.T) {
    // 1. 创建兑换码
    promotionCode := &model.PromotionCode{
        Code:              "TEST_CODE_123",
        Days:              30,
        VipLevel:          100, // PRO会员
        Status:            0,   // 未使用
        CreateTimestamp:   time.Now().UnixMilli(),
    }
    
    err := db.Create(promotionCode).Error
    assert.NoError(t, err)
    
    // 2. 使用兑换码
    err = vipModel.ExchangeCode("test_user_123", "TEST_CODE_123")
    assert.NoError(t, err)
    
    // 3. 验证兑换码状态
    var updatedCode model.PromotionCode
    err = db.Where("code = ?", "TEST_CODE_123").First(&updatedCode).Error
    assert.NoError(t, err)
    assert.Equal(t, 1, updatedCode.Status) // 已使用
    assert.Equal(t, "test_user_123", updatedCode.Uid)
    
    // 4. 验证会员状态
    userVip, found, err := vipModel.GetUserVIPRelations("test_user_123")
    assert.NoError(t, err)
    assert.True(t, found)
    assert.Equal(t, 1, userVip.IsVip)
    assert.Equal(t, uint(100), userVip.VipID)
}
```

### 4.3 苹果内购通知处理测试

#### 4.3.1 首次购买通知处理
```go
func TestAppleInitialBuyNotification(t *testing.T) {
    // 模拟首次购买通知
    notification := &apple.NotificationV2Payload{
        NotificationType: "SUBSCRIBED",
        Subtype:          "INITIAL_BUY",
        // 其他必要字段
    }
    
    err := vipModel.ReceiveApplePayResult(notification, nil, nil)
    assert.NoError(t, err)
    
    // 验证订阅记录创建
    var subscription model.UserSubscription
    err = db.Where("uid = ?", "test_user_123").First(&subscription).Error
    assert.NoError(t, err)
    assert.Equal(t, 1, subscription.Status) // 签约状态
}
```

#### 4.3.2 续订通知处理
```go
func TestAppleRenewalNotification(t *testing.T) {
    // 模拟续订通知
    notification := &apple.NotificationV2Payload{
        NotificationType: "DID_RENEW",
        // 其他必要字段
    }
    
    err := vipModel.ReceiveApplePayResult(notification, nil, nil)
    assert.NoError(t, err)
    
    // 验证续订处理
    var subscription model.UserSubscription
    err = db.Where("uid = ?", "test_user_123").First(&subscription).Error
    assert.NoError(t, err)
    // 验证下次支付时间更新
}
```

## 5. 性能测试

### 5.1 并发测试

#### 5.1.1 并发创建订单测试
```go
func TestConcurrentCreateOrder(t *testing.T) {
    const numGoroutines = 100
    var wg sync.WaitGroup
    errors := make(chan error, numGoroutines)
    
    for i := 0; i < numGoroutines; i++ {
        wg.Add(1)
        go func(index int) {
            defer wg.Done()
            
            uid := fmt.Sprintf("test_user_%d", index)
            orderNo, err := vipModel.CreatePayOrder(uid, 1, 2)
            if err != nil {
                errors <- err
                return
            }
            
            if orderNo == "" {
                errors <- fmt.Errorf("empty order number")
            }
        }(i)
    }
    
    wg.Wait()
    close(errors)
    
    // 检查是否有错误
    for err := range errors {
        t.Errorf("并发测试失败: %v", err)
    }
}
```

#### 5.1.2 并发兑换码测试
```go
func TestConcurrentExchangeCode(t *testing.T) {
    // 测试同一兑换码的并发使用
    const numGoroutines = 10
    var wg sync.WaitGroup
    errors := make(chan error, numGoroutines)
    
    for i := 0; i < numGoroutines; i++ {
        wg.Add(1)
        go func(index int) {
            defer wg.Done()
            
            uid := fmt.Sprintf("test_user_%d", index)
            err := vipModel.ExchangeCode(uid, "CONCURRENT_TEST_CODE")
            if err != nil {
                errors <- err
            }
        }(i)
    }
    
    wg.Wait()
    close(errors)
    
    // 验证只有一个用户成功兑换
    var successCount int
    for err := range errors {
        if err == nil {
            successCount++
        }
    }
    
    assert.Equal(t, 1, successCount, "应该只有一个用户成功兑换")
}
```

### 5.2 数据库性能测试

#### 5.2.1 大量数据查询测试
```go
func TestLargeDataQuery(t *testing.T) {
    // 测试大量会员数据的查询性能
    start := time.Now()
    
    var userVips []model.UserVIPRelations
    err := db.Find(&userVips).Error
    assert.NoError(t, err)
    
    duration := time.Since(start)
    t.Logf("查询 %d 条会员记录耗时: %v", len(userVips), duration)
    
    // 性能要求：查询1000条记录应在1秒内完成
    assert.Less(t, duration, time.Second)
}
```

## 6. 安全测试

### 6.1 输入验证测试

#### 6.1.1 参数验证测试
```go
func TestInputValidation(t *testing.T) {
    // 测试无效参数
    testCases := []struct {
        name        string
        productId   uint
        expectedErr string
    }{
        {"无效商品ID", 999999, "商品不存在"},
        {"零商品ID", 0, "商品ID不能为空"},
    }
    
    for _, tc := range testCases {
        t.Run(tc.name, func(t *testing.T) {
            _, err := vipModel.CreatePayOrder("test_user", tc.productId, 2)
            assert.Error(t, err)
            assert.Contains(t, err.Error(), tc.expectedErr)
        })
    }
}
```

#### 6.1.2 SQL注入防护测试
```go
func TestSQLInjectionProtection(t *testing.T) {
    // 测试SQL注入防护
    maliciousInput := "'; DROP TABLE users; --"
    
    // 尝试使用恶意输入查询
    var userVip model.UserVIPRelations
    err := db.Where("uid = ?", maliciousInput).First(&userVip).Error
    
    // 应该返回记录未找到错误，而不是SQL错误
    assert.Error(t, err)
    assert.NotContains(t, err.Error(), "DROP TABLE")
}
```

### 6.2 权限测试

#### 6.2.1 未授权访问测试
```go
func TestUnauthorizedAccess(t *testing.T) {
    // 测试未授权访问会员信息
    req := httptest.NewRequest("GET", "/api/vip/info", nil)
    // 不设置Authorization头
    
    w := httptest.NewRecorder()
    router.ServeHTTP(w, req)
    
    assert.Equal(t, http.StatusUnauthorized, w.Code)
}
```

#### 6.2.2 越权访问测试
```go
func TestCrossUserAccess(t *testing.T) {
    // 测试用户A访问用户B的会员信息
    req := httptest.NewRequest("GET", "/api/vip/info", nil)
    req.Header.Set("Authorization", "Bearer " + userAToken)
    
    w := httptest.NewRecorder()
    router.ServeHTTP(w, req)
    
    // 应该只能访问自己的信息
    var response map[string]interface{}
    json.Unmarshal(w.Body.Bytes(), &response)
    assert.True(t, response["success"].(bool))
    
    // 验证返回的是用户A的信息，不是用户B的
}
```

## 7. 边界条件测试

### 7.1 数据边界测试

#### 7.1.1 会员到期边界测试
```go
func TestVipExpirationBoundary(t *testing.T) {
    // 测试会员即将到期的情况
    userVip := &model.UserVIPRelations{
        Uid:             "test_user_123",
        IsVip:           1,
        VipID:           100,
        ExpireTimestamp: time.Now().Add(time.Minute).UnixMilli(), // 1分钟后到期
    }
    
    err := db.Create(userVip).Error
    assert.NoError(t, err)
    
    // 验证会员状态
    assert.Equal(t, 1, userVip.IsVip)
    
    // 等待到期后验证状态
    time.Sleep(time.Minute + time.Second)
    
    var updatedUserVip model.UserVIPRelations
    err = db.Where("uid = ?", "test_user_123").First(&updatedUserVip).Error
    assert.NoError(t, err)
    // 注意：这里需要实现自动到期检查逻辑
}
```

#### 7.1.2 权益数量边界测试
```go
func TestBenefitCountBoundary(t *testing.T) {
    // 测试权益数量为0的情况
    userBenefit := &model.UserBenefit{
        Uid:              "test_user_123",
        BenefitGroupCode: "AI_TRANSLATION",
        BenefitID:        1,
        Remain:           0, // 剩余数量为0
        Total:            5,
        Status:           1,
    }
    
    err := db.Create(userBenefit).Error
    assert.NoError(t, err)
    
    // 尝试消耗权益
    err = benefitModel.ConsumeBenefit("test_user_123", "AI_TRANSLATION", 1)
    assert.Error(t, err)
    assert.Contains(t, err.Error(), "权益剩余次数不足")
}
```

### 7.2 时间边界测试

#### 7.2.1 权益刷新时间测试
```go
func TestBenefitRefreshTimeBoundary(t *testing.T) {
    // 测试权益刷新时间边界
    now := time.Now()
    userBenefit := &model.UserBenefit{
        Uid:                  "test_user_123",
        BenefitGroupCode:     "AI_TRANSLATION",
        BenefitID:            1,
        Remain:               0,
        Total:                5,
        Status:               1,
        NextRefreshTime:      now.Add(time.Second), // 1秒后刷新
        NextRefreshTimestamp: now.Add(time.Second).UnixMilli(),
    }
    
    err := db.Create(userBenefit).Error
    assert.NoError(t, err)
    
    // 等待刷新时间
    time.Sleep(time.Second + time.Millisecond)
    
    // 尝试消耗权益，应该自动刷新
    err = benefitModel.ConsumeBenefit("test_user_123", "AI_TRANSLATION", 1)
    assert.NoError(t, err)
    
    // 验证权益已刷新
    var updatedBenefit model.UserBenefit
    err = db.Where("uid = ? AND benefit_group_code = ?", "test_user_123", "AI_TRANSLATION").First(&updatedBenefit).Error
    assert.NoError(t, err)
    assert.Equal(t, 4, updatedBenefit.Remain) // 5-1=4
}
```

## 8. 错误处理测试

### 8.1 数据库错误测试

#### 8.1.1 数据库连接失败测试
```go
func TestDatabaseConnectionFailure(t *testing.T) {
    // 模拟数据库连接失败
    // 需要mock数据库连接
    
    _, err := vipModel.CreatePayOrder("test_user", 1, 2)
    assert.Error(t, err)
    assert.Contains(t, err.Error(), "数据库连接失败")
}
```

#### 8.1.2 事务回滚测试
```go
func TestTransactionRollback(t *testing.T) {
    // 测试事务回滚
    err := vipModel.ExchangeCode("test_user_123", "INVALID_CODE")
    assert.Error(t, err)
    
    // 验证没有创建任何记录
    var userVip model.UserVIPRelations
    err = db.Where("uid = ?", "test_user_123").First(&userVip).Error
    assert.Error(t, err) // 应该找不到记录
}
```

### 8.2 业务逻辑错误测试

#### 8.2.1 重复兑换码测试
```go
func TestDuplicateExchangeCode(t *testing.T) {
    // 测试重复使用兑换码
    code := "DUPLICATE_TEST_CODE"
    
    // 第一次使用
    err := vipModel.ExchangeCode("test_user_1", code)
    assert.NoError(t, err)
    
    // 第二次使用同一兑换码
    err = vipModel.ExchangeCode("test_user_2", code)
    assert.Error(t, err)
    assert.Contains(t, err.Error(), "兑换码已使用")
}
```

#### 8.2.2 无效兑换码测试
```go
func TestInvalidExchangeCode(t *testing.T) {
    // 测试无效兑换码
    err := vipModel.ExchangeCode("test_user_123", "INVALID_CODE")
    assert.Error(t, err)
    assert.Contains(t, err.Error(), "兑换码不存在")
}
```

## 9. 兼容性测试

### 9.1 平台兼容性测试

#### 9.1.1 iOS平台测试
```go
func TestIOSPlatform(t *testing.T) {
    // 测试iOS平台特定功能
    req := httptest.NewRequest("GET", "/api/vip/products", nil)
    req.Header.Set("Platform", "ios")
    
    w := httptest.NewRecorder()
    router.ServeHTTP(w, req)
    
    assert.Equal(t, http.StatusOK, w.Code)
    
    var response map[string]interface{}
    json.Unmarshal(w.Body.Bytes(), &response)
    
    // 验证返回的是iOS商品
    products := response["data"].([]interface{})
    for _, product := range products {
        productMap := product.(map[string]interface{})
        assert.Equal(t, float64(2), productMap["terminal"]) // iOS终端
    }
}
```

#### 9.1.2 Android平台测试
```go
func TestAndroidPlatform(t *testing.T) {
    // 测试Android平台特定功能
    req := httptest.NewRequest("GET", "/api/vip/products", nil)
    req.Header.Set("Platform", "android")
    
    w := httptest.NewRecorder()
    router.ServeHTTP(w, req)
    
    assert.Equal(t, http.StatusOK, w.Code)
    
    var response map[string]interface{}
    json.Unmarshal(w.Body.Bytes(), &response)
    
    // 验证返回的是Android商品
    products := response["data"].([]interface{})
    for _, product := range products {
        productMap := product.(map[string]interface{})
        assert.Equal(t, float64(1), productMap["terminal"]) // Android终端
    }
}
```

## 10. 监控和日志测试

### 10.1 日志记录测试

#### 10.1.1 操作日志测试
```go
func TestOperationLogging(t *testing.T) {
    // 测试操作日志记录
    // 需要mock日志系统
    
    // 执行会员操作
    err := vipModel.ExchangeCode("test_user_123", "LOG_TEST_CODE")
    assert.NoError(t, err)
    
    // 验证日志记录
    // 检查是否记录了相应的日志条目
}
```

### 10.2 性能监控测试

#### 10.2.1 响应时间监控
```go
func TestResponseTimeMonitoring(t *testing.T) {
    // 测试响应时间监控
    start := time.Now()
    
    req := httptest.NewRequest("GET", "/api/vip/info", nil)
    req.Header.Set("Authorization", "Bearer " + testToken)
    
    w := httptest.NewRecorder()
    router.ServeHTTP(w, req)
    
    duration := time.Since(start)
    
    // 验证响应时间在合理范围内
    assert.Less(t, duration, 100*time.Millisecond)
}
```

## 11. 测试数据管理

### 11.1 测试数据准备

#### 11.1.1 基础测试数据
```go
func setupTestData(t *testing.T) {
    // 创建测试商品
    products := []model.TradeProduct{
        {
            Name:           "月度会员",
            Type:           1,
            IsSubscription: 1,
            Price:          9.99,
            OriginPrice:    19.99,
            IosProductId:   "com.app.monthly",
            Currency:       "USD",
            Terminal:       2, // iOS
            Days:           30,
            VipID:          100,
        },
        {
            Name:           "年度会员",
            Type:           1,
            IsSubscription: 1,
            Price:          99.99,
            OriginPrice:    199.99,
            IosProductId:   "com.app.yearly",
            Currency:       "USD",
            Terminal:       2, // iOS
            Days:           365,
            VipID:          1000,
        },
    }
    
    for _, product := range products {
        err := db.Create(&product).Error
        assert.NoError(t, err)
    }
    
    // 创建测试兑换码
    codes := []model.PromotionCode{
        {
            Code:            "TEST_CODE_30",
            Days:            30,
            VipLevel:        100,
            Status:          0,
            CreateTimestamp: time.Now().UnixMilli(),
        },
        {
            Code:            "TEST_CODE_365",
            Days:            365,
            VipLevel:        1000,
            Status:          0,
            CreateTimestamp: time.Now().UnixMilli(),
        },
    }
    
    for _, code := range codes {
        err := db.Create(&code).Error
        assert.NoError(t, err)
    }
}
```

#### 11.1.2 测试数据清理
```go
func cleanupTestData(t *testing.T) {
    // 清理测试数据
    tables := []string{
        "user_vip_relations",
        "user_vip_flows",
        "user_purchase_orders",
        "user_subscriptions",
        "promotion_codes",
        "user_benefits",
        "user_benefit_logs",
    }
    
    for _, table := range tables {
        err := db.Exec("DELETE FROM " + table + " WHERE uid LIKE 'test_user_%'").Error
        assert.NoError(t, err)
    }
}
```

## 12. 测试执行计划

### 12.1 测试优先级

#### 12.1.1 高优先级测试（P0）
- 会员购买流程
- 兑换码使用流程
- 苹果内购通知处理
- 权益分配和消耗
- 基础API接口功能

#### 12.1.2 中优先级测试（P1）
- 并发处理
- 错误处理
- 安全测试
- 性能测试

#### 12.1.3 低优先级测试（P2）
- 边界条件测试
- 兼容性测试
- 监控和日志测试

### 12.2 测试执行顺序

1. **单元测试** - 验证各个模块的基本功能
2. **集成测试** - 验证模块间的协作
3. **业务流程测试** - 验证完整的业务场景
4. **性能测试** - 验证系统性能指标
5. **安全测试** - 验证系统安全性
6. **兼容性测试** - 验证多平台兼容性

### 12.3 测试环境配置

#### 12.3.1 测试数据库配置
```yaml
test_database:
  host: localhost
  port: 3306
  username: test_user
  password: test_password
  database: lsenglish_test
  charset: utf8mb4
```

#### 12.3.2 测试Redis配置
```yaml
test_redis:
  host: localhost
  port: 6379
  password: ""
  database: 1
```

## 13. 测试报告模板

### 13.1 测试执行报告

| 测试类别 | 总用例数 | 通过数 | 失败数 | 通过率 |
|---------|---------|--------|--------|--------|
| 单元测试 | 50 | 48 | 2 | 96% |
| 集成测试 | 30 | 28 | 2 | 93% |
| 业务流程测试 | 20 | 19 | 1 | 95% |
| 性能测试 | 10 | 9 | 1 | 90% |
| 安全测试 | 15 | 15 | 0 | 100% |
| **总计** | **125** | **119** | **6** | **95.2%** |

### 13.2 缺陷统计

| 严重程度 | 数量 | 状态 |
|---------|------|------|
| 严重 | 1 | 已修复 |
| 高 | 2 | 已修复 |
| 中 | 2 | 待修复 |
| 低 | 1 | 待修复 |

### 13.3 性能指标

| 指标 | 目标值 | 实际值 | 状态 |
|------|--------|--------|------|
| API响应时间 | <100ms | 85ms | ✅ |
| 并发用户数 | 1000 | 1200 | ✅ |
| 数据库查询时间 | <50ms | 35ms | ✅ |
| 内存使用 | <512MB | 480MB | ✅ |

## 14. 持续集成测试

### 14.1 CI/CD配置

#### 14.1.1 GitHub Actions配置
```yaml
name: 会员系统测试

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: lsenglish_test
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
      
      redis:
        image: redis:6
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.19'
    
    - name: Install dependencies
      run: go mod download
    
    - name: Run unit tests
      run: go test -v ./test/...
      env:
        DB_HOST: localhost
        DB_PORT: 3306
        DB_USER: root
        DB_PASSWORD: root
        DB_NAME: lsenglish_test
    
    - name: Run integration tests
      run: go test -v -tags=integration ./test/...
    
    - name: Generate test coverage
      run: go test -coverprofile=coverage.out ./...
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.out
```

### 14.2 自动化测试脚本

#### 14.2.1 测试执行脚本
```bash
#!/bin/bash

# 会员系统测试执行脚本

echo "开始执行会员系统测试..."

# 设置测试环境
export TEST_ENV=ci
export DB_HOST=localhost
export DB_PORT=3306
export DB_USER=test_user
export DB_PASSWORD=test_password
export DB_NAME=lsenglish_test

# 运行单元测试
echo "执行单元测试..."
go test -v ./test/unit/...

# 运行集成测试
echo "执行集成测试..."
go test -v ./test/integration/...

# 运行性能测试
echo "执行性能测试..."
go test -v ./test/performance/...

# 生成测试报告
echo "生成测试报告..."
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out -o coverage.html

echo "测试执行完成！"
```

## 15. 总结

本测试文档涵盖了会员系统的各个方面，包括：

1. **全面的测试覆盖** - 从单元测试到集成测试，从功能测试到性能测试
2. **详细的测试用例** - 每个测试用例都有明确的输入、预期输出和验证点
3. **实用的测试工具** - 提供了测试数据管理、CI/CD配置等实用工具
4. **清晰的执行计划** - 明确了测试优先级和执行顺序
5. **完整的报告模板** - 提供了测试报告和缺陷统计的模板

通过执行这些测试，可以确保会员系统的稳定性、安全性和性能，为用户提供可靠的会员服务体验。 