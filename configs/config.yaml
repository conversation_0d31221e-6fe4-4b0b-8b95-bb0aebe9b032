Env: debug # 环境，线上环境：release、测试环境：debug
BaseUrl: 127.0.0.1 # 网站域名
Port: 3000 # 端口
LogPath: /Users/<USER>/Documents/GitHub/lsenglish_backend/deploy/logs/

Server:
  RunMode: debug
  HttpPort: 3000
  ReadTimeout: 60
  WriteTimeout: 60

AI:
  MaxResourcesPerBatch: 50

DB:
  Url: root:123456@tcp(127.0.0.1:3306)/loop?charset=utf8mb4&parseTime=True&loc=Local
  MaxIdleConns: 50
  MaxOpenConns: 200

Redis:
  Host: 127.0.0.1:6379
  Password: 123456
  MaxIdle: 50
  MaxActive: 100
  IdleTimeout: 300
  DialTimeout: 30
  ReadTimeout: 10
  WriteTimeout: 10
  PoolTimeout: 30

Jwt:
  SignKey: testjwtsingkley111
  ExpireSeconds: 2592000

JwtAdmin:
  SignKey: adminjwtmiaoyongjun
  ExpireSeconds: 8640000 #86400为一天

Limit:
  EnableIpLimit: true
  EnableUserLimit: true

Setting:
  Language: es
  AliyunOss:
      Host: 请配置成你自己的
      Bucket: 请配置成你自己的
      Endpoint: 请配置成你自己的
      AccessId: 请配置成你自己的
      AccessSecret: 请配置成你自己的
      Region: cn-hangzhou
