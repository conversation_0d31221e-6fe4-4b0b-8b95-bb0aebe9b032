# LSEnglish Backend 代码规范文档

## 项目架构概述

### 技术栈
- **编程语言**: Go 1.23.0
- **Web框架**: Gin
- **数据库**: MySQL (使用GORM ORM)
- **缓存**: Redis
- **依赖注入**: Google Wire
- **日志**: Logrus
- **配置管理**: YAML配置文件
- **认证**: JWT
- **文件存储**: 阿里云OSS
- **AI服务**: 火山引擎

### 架构模式
项目采用**分层架构**模式，遵循**依赖注入**和**接口分离**原则：

```
cmd/                    # 应用入口
├── main.go            # 主程序入口
├── wire.go            # 依赖注入配置
└── wire_gen.go        # Wire生成的代码

internal/              # 内部业务逻辑
├── api/               # 控制器层 (Controller)
├── data/              # 数据访问层 (Repository)
├── model/             # 数据模型层 (Model)
├── request/           # 请求DTO
├── response/          # 响应DTO
├── config/            # 配置管理
├── constants/         # 常量定义
├── server/            # 服务器配置
└── app/               # 应用生命周期管理

pkg/                   # 公共工具包
├── web/               # Web响应工具
├── util/              # 通用工具
├── logs/              # 日志工具
├── jwtx/              # JWT工具
└── ...                # 其他工具包

middleware/            # 中间件
test/                  # 测试文件
configs/               # 配置文件
```

## 新模块开发流程

### 1. 创建数据模型 (Model)
**位置**: `internal/model/`

```go
// internal/model/example.go
package model

type Example struct {
    Model                                    // 继承基础模型
    Name        string `gorm:"size:128;not null;comment:名称"`
    Description string `gorm:"size:500;comment:描述"`
    Status      int    `gorm:"default:0;comment:状态"`
}

func (Example) TableName() string {
    return "examples"
}

// 创建模型构造函数
func NewExampleModel(db *DbModel) *ExampleModel {
    return &ExampleModel{DbModel: db}
}

type ExampleModel struct {
    *DbModel
}
```

**必需步骤**:
1. 在 `internal/model/models.go` 中添加模型到 `Models` 切片
2. 在 `internal/model/model.go` 的 `ProviderSet` 中添加构造函数

### 2. 创建请求/响应DTO
**位置**: `internal/request/` 和 `internal/response/`

```go
// internal/request/example.go
package request

type CreateExampleReq struct {
    Name        string `json:"name" binding:"required,min=2,max=128"`
    Description string `json:"description" binding:"max=500"`
}

type UpdateExampleReq struct {
    Id          string `json:"id" binding:"required"`
    Name        string `json:"name" binding:"required,min=2,max=128"`
    Description string `json:"description" binding:"max=500"`
}

// internal/response/example.go
package response

type ExampleResp struct {
    Id          string `json:"id"`
    Name        string `json:"name"`
    Description string `json:"description"`
    Status      int    `json:"status"`
    CreatedAt   string `json:"createdAt"`
}
```

### 3. 创建数据访问层 (Repository)
**位置**: `internal/data/`

```go
// internal/data/example.go
package data

import (
    "loop/internal/model"
    "loop/internal/request"
    "loop/internal/response"
    "loop/pkg/web"
    "github.com/gin-gonic/gin"
    "github.com/jinzhu/copier"
)

func NewExampleRepo(model *model.ExampleModel) *ExampleRepo {
    return &ExampleRepo{model: model}
}

type ExampleRepo struct {
    model *model.ExampleModel
}

func (r *ExampleRepo) Create(c *gin.Context, req request.CreateExampleReq) *web.JsonResult {
    var example model.Example
    if err := copier.Copy(&example, &req); err != nil {
        return web.JsonInternalError(err)
    }
    
    if err := r.model.Save(&example).Error; err != nil {
        return web.JsonInternalError(err)
    }
    
    var resp response.ExampleResp
    copier.Copy(&resp, &example)
    return web.JsonData(resp)
}

func (r *ExampleRepo) GetList(pageSize, currentPage int) *web.JsonResult {
    return GetListWithResp[model.Example, response.ExampleResp](r.model.DbModel, pageSize, currentPage)
}
```

**必需步骤**:
1. 在 `internal/data/data.go` 的 `ProviderSet` 中添加构造函数

### 4. 创建控制器层 (API)
**位置**: `internal/api/`

```go
// internal/api/example.go
package api

import (
    "loop/internal/data"
    "loop/internal/request"
    "github.com/gin-gonic/gin"
)

func NewExampleApi(repo *data.ExampleRepo) *ExampleApi {
    return &ExampleApi{repo: repo}
}

type ExampleApi struct {
    Apis
    repo *data.ExampleRepo
}

func (a *ExampleApi) Create(c *gin.Context) {
    var req request.CreateExampleReq
    a.processRequest(c, &req, func(r any) any {
        return a.repo.Create(c, req)
    })
}

func (a *ExampleApi) GetList(c *gin.Context) {
    var req request.PageReq
    a.processRequestQuery(c, &req, func(r any) any {
        return a.repo.GetList(req.PageSize, req.CurrentPage)
    })
}
```

**必需步骤**:
1. 在 `internal/api/api.go` 的 `ProviderSet` 中添加构造函数

### 5. 注册路由
**位置**: `internal/server/router.go`

```go
// 在 NewRegisterHTTPServer 函数中添加参数
exampleApi *api.ExampleApi,

// 在路由注册部分添加
addExample(config, exampleApi, v1, protected, adminProtected)

// 添加路由注册函数
func addExample(
    config *config.Config,
    exampleApi *api.ExampleApi,
    rg *gin.RouterGroup,
    protected *gin.RouterGroup,
    adminProtected *gin.RouterGroup,
) {
    protected.POST("example", exampleApi.Create)
    protected.GET("examples", exampleApi.GetList)
    
    adminProtected.PUT("example", exampleApi.Update)
    adminProtected.DELETE("example", exampleApi.Delete)
}
```

## 代码组织规范

### 目录结构说明

| 目录 | 用途 | 说明 |
|------|------|------|
| `cmd/` | 应用入口 | 包含main.go和依赖注入配置 |
| `internal/api/` | 控制器层 | 处理HTTP请求，参数验证，调用业务逻辑 |
| `internal/data/` | 数据访问层 | 数据库操作，业务逻辑实现 |
| `internal/model/` | 数据模型 | GORM模型定义，数据库表结构 |
| `internal/request/` | 请求DTO | API请求参数结构体 |
| `internal/response/` | 响应DTO | API响应数据结构体 |
| `internal/data/util/` | 数据层工具 | 数据处理相关的工具函数 |
| `pkg/` | 公共工具包 | 可复用的工具函数和类型 |
| `middleware/` | 中间件 | HTTP中间件，如认证、日志、限流等 |
| `configs/` | 配置文件 | YAML配置文件 |
| `test/` | 测试文件 | 单元测试和集成测试 |

### 文件命名规范

1. **文件名**: 使用小写字母和下划线，如 `user_admin.go`
2. **包名**: 使用小写字母，与目录名一致
3. **结构体**: 使用大驼峰命名，如 `UserLoginReq`
4. **函数/方法**: 公开函数使用大驼峰，私有函数使用小驼峰
5. **常量**: 使用大驼峰或全大写，如 `UserStatusNormal` 或 `ACTIVE`
6. **变量**: 使用小驼峰命名

### 代码分层原则

1. **控制器层 (API)**: 
   - 只处理HTTP请求/响应
   - 参数验证和绑定
   - 调用数据层方法
   - 不包含业务逻辑

2. **数据访问层 (Data)**:
   - 实现具体业务逻辑
   - 数据库操作
   - 数据转换和处理
   - 错误处理

3. **模型层 (Model)**:
   - 定义数据结构
   - 数据库表映射
   - 基础的数据验证

## 开发规范

### 错误处理规范

1. **统一错误响应**:
```go
// 使用 pkg/web 包中的标准响应
return web.JsonInternalError(err)     // 内部错误
return web.JsonParamErr("参数错误")    // 参数错误
return web.JsonError("业务错误")       // 业务错误
return web.JsonOK()                   // 成功响应
return web.JsonData(data)             // 成功响应带数据
```

2. **错误日志记录**:
```go
// 使用 logrus 记录错误
logrus.WithError(err).Error("操作失败")
logrus.WithFields(logrus.Fields{
    "userId": userId,
    "action": "create_user",
}).Error("创建用户失败")
```

### 日志记录规范

1. **日志级别**:
   - `Error`: 系统错误，需要立即关注
   - `Warn`: 警告信息，可能的问题
   - `Info`: 一般信息，业务流程记录
   - `Debug`: 调试信息，仅在开发环境

2. **日志格式**:
```go
// 结构化日志
logrus.WithFields(logrus.Fields{
    "userId": "123",
    "action": "login",
    "ip": "***********",
}).Info("用户登录成功")

// 错误日志
logrus.WithError(err).WithFields(logrus.Fields{
    "userId": userId,
    "operation": "update_profile",
}).Error("更新用户资料失败")
```

### 数据验证规范

1. **请求参数验证**:
```go
type CreateUserReq struct {
    Username string `json:"username" binding:"required,min=5,max=30"`
    Password string `json:"password" binding:"required,min=8,max=40"`
    Email    string `json:"email" binding:"required,email"`
    Age      int    `json:"age" binding:"min=1,max=120"`
}
```

2. **自定义验证器**:
```go
// 在 pkg/util/validator.go 中定义
var CustomValidator validator.Func = func(fl validator.FieldLevel) bool {
    // 验证逻辑
    return true
}

// 注册验证器
func RegisterCustomValidators(v *validator.Validate) {
    v.RegisterValidation("custom", CustomValidator)
}
```

### API接口设计规范

1. **RESTful API设计**:
```
GET    /api/v1/users          # 获取用户列表
GET    /api/v1/user/:id       # 获取单个用户
POST   /api/v1/user           # 创建用户
PUT    /api/v1/user/:id       # 更新用户
DELETE /api/v1/user/:id       # 删除用户
```

2. **响应格式**:
```json
{
  "code": 200,
  "msg": "",
  "data": {
    // 响应数据
  }
}

// 分页响应
{
  "code": 200,
  "msg": "",
  "data": {
    "total": 100,
    "data": [
      // 列表数据
    ]
  }
}
```

3. **认证和权限**:
```go
// 需要认证的路由
protected.GET("user", userApi.GetById)

// 需要管理员权限的路由
adminProtected.GET("users", userAdminApi.GetList)
```

## 技术栈使用指南

### GORM使用规范

1. **模型定义**:
```go
type User struct {
    Model                                    // 继承基础模型
    Username string `gorm:"size:64;not null;index;comment:用户名"`
    Email    string `gorm:"size:128;uniqueIndex;comment:邮箱"`
    Status   int    `gorm:"default:0;comment:状态"`
}

func (User) TableName() string {
    return "users"
}
```

2. **数据库操作**:
```go
// 使用模型包装器
func (r *UserRepo) Create(user *model.User) error {
    return r.model.Save(user).Error
}

func (r *UserRepo) GetByID(id string) (*model.User, error) {
    var user model.User
    found, err := r.model.GetOne(&user, "id = ?", id)
    if !found {
        return nil, errors.New("用户不存在")
    }
    return &user, err
}
```

### Redis使用规范

1. **缓存键命名**:
```go
const (
    UserTokenPrefix = "user:token:"
    UserCachePrefix = "user:cache:"
)

// 使用
key := constants.UserTokenPrefix + userId
```

2. **缓存操作**:
```go
// 设置缓存
err := client.RedisClient.Set(ctx, key, value, time.Hour).Err()

// 获取缓存
value, err := client.RedisClient.Get(ctx, key).Result()
if err == redis.Nil {
    // 缓存不存在
}
```

### 配置管理规范

1. **配置文件结构**:
```yaml
# configs/config.yaml
Env: debug
Port: 3000

DB:
  Url: "root:password@tcp(localhost:3306)/database"
  MaxIdleConns: 50
  MaxOpenConns: 200

Redis:
  Host: "localhost:6379"
  Password: ""
  MaxIdle: 50
```

2. **配置结构体**:
```go
type Config struct {
    Env      string      `mapstructure:"env"`
    Port     int         `mapstructure:"port"`
    DbConfig DbConfig    `mapstructure:"db"`
    Redis    RedisConfig `mapstructure:"redis"`
}
```

## 测试规范

### 单元测试编写规范

1. **测试文件命名**: `*_test.go`
2. **测试函数命名**: `TestFunctionName`
3. **测试结构**:

```go
package test

import (
    "testing"
    "github.com/stretchr/testify/assert"
)

func TestUserCreate(t *testing.T) {
    t.Run("成功创建用户", func(t *testing.T) {
        // Arrange
        req := request.CreateUserReq{
            Username: "testuser",
            Password: "password123",
        }
        
        // Act
        result := userRepo.Create(ctx, req)
        
        // Assert
        assert.NotNil(t, result)
        assert.Equal(t, http.StatusOK, result.Code)
    })
    
    t.Run("用户名已存在", func(t *testing.T) {
        // 测试用户名冲突的情况
    })
}
```

### 测试文件组织方式

```
test/
├── README.md
├── users_test.go          # 用户相关测试
├── resources_test.go      # 资源相关测试
└── integration/           # 集成测试
    ├── api_test.go
    └── database_test.go
```

### 测试命名规范

1. **测试函数**: `Test + 功能名称`
2. **基准测试**: `Benchmark + 功能名称`
3. **示例测试**: `Example + 功能名称`

## 最佳实践

### 1. 依赖注入
- 使用Google Wire进行依赖注入
- 在各层的构造函数中注入依赖
- 避免全局变量和单例模式

### 2. 错误处理
- 使用统一的错误响应格式
- 记录详细的错误日志
- 区分业务错误和系统错误

### 3. 性能优化
- 使用Redis缓存热点数据
- 数据库查询优化，避免N+1问题
- 使用连接池管理数据库连接

### 4. 安全性
- 所有API都要进行认证和授权检查
- 输入参数验证和SQL注入防护
- 敏感信息加密存储

### 5. 代码质量
- 保持函数简洁，单一职责
- 添加必要的注释和文档
- 定期进行代码重构和优化

---

本文档将随着项目发展持续更新，请开发团队严格遵循以上规范，确保代码质量和项目的可维护性。
